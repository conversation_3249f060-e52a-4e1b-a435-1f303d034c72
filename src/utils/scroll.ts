/**
 * 针对dom需要自动滚动封装
 *
 * 传入需要自动滚动的dom，返回3个函数：stop, pause, start
 * 注意需要手动销毁定时器
 *
 * @param dom
 */
export const autoScroll = (dom: HTMLElement, speed = 90) => {
  if (!dom) {
    console.error("dom is null or height is 0");
    return null;
  }
  let flag = true,
    tempPauseFlag = false;
  let timer = setInterval(() => {
    if (!dom.scrollHeight) {
      return;
    }
    if (!flag || tempPauseFlag) {
      return;
    }
    if (dom.scrollTop + dom.clientHeight + 1 >= dom.scrollHeight) {
      dom.scrollTop = 0;
      tempPauseFlag = true;
      setTimeout(() => {
        tempPauseFlag = false;
      }, 2000);
    } else {
      dom.scrollTop++;
    }
  }, speed);
  dom.onmouseenter = () => {
    flag = false;
  };
  dom.onmouseleave = () => {
    flag = true;
  };
  const stop = () => {
    clearInterval(timer);
  };
  const pause = () => {
    flag = false;
  };
  const start = () => {
    flag = true;
  };

  return {
    timer,
    stop,
    pause,
    start
  };
};
