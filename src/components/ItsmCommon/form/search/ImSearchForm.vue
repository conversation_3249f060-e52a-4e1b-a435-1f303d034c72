<template>
  <div ref="searchFormEl" class="im-search-form" :style="searchMainStyle">
    <div v-if="!onlyExpandMode" style="display: flex">
      <div class="search-area" :style="searchStyle || {}">
        <div class="search-area-input">
          <!-- 字段选择 -->
          <el-select
            class="column-select"
            :style="{ width: selectWidth }"
            v-model="columnField"
            filterable
            :disabled="expanded"
            placeholder="请选择"
          >
            <el-option
              v-for="col in searchColumns"
              :key="col.field"
              :label="col.name"
              :value="col.field"
              @click.native="changeSearchColumn(col)"
            >
            </el-option>
          </el-select>

          <el-divider direction="vertical"></el-divider>

          <!-- 文本输入搜索 -->
          <el-input
            v-if="useInputOnSearch || isComponentType(column, 'Input')"
            v-bind="componentProps(column)"
            v-model="searchValue"
            :disabled="expanded"
            :autosize="false"
            :rows="1"
          >
            <template v-if="fuzzyable(column)" #append>
              <el-checkbox
                :disabled="expanded"
                v-model="fuzzy"
                true-label="fuzzy"
                false-label="exact"
                >模糊
              </el-checkbox>
            </template>
          </el-input>

          <!-- 数字框使用普通文本框+number替代 -->
          <el-input
            v-else-if="isComponentType(column, 'Number')"
            class="prpendable-number"
            v-bind="componentProps(column)"
            type="number"
            :disabled="expanded"
            v-model.number="searchValue"
            style="width: 100%"
          >
            <template v-if="column.component.prepend" #prepend>
              <el-select
                v-model="numberOps"
                placeholder="运算符"
                style="width: 100px"
              >
                <el-option
                  v-for="(option, index) in column.component.prependOptions"
                  :key="index"
                  v-bind="option"
                ></el-option>
              </el-select>
            </template>
          </el-input>

          <!-- 下拉树,单选框，多选框在折叠视图下统一使用下拉，单选框和多选框在折叠时风格不搭  -->
          <el-select
            v-else-if="isComponentType(column, 'Select', ['Radio', 'Checkbox'])"
            v-bind="componentProps(column)"
            v-model="searchValue"
            style="width: 100%"
            filterable
            :disabled="expanded"
            @change="$nextTick(doSearch)"
          >
            <template
              v-for="(option, index) in column.component.options"
              :key="index"
            >
              <el-option
                v-if="isRenderOption(option)"
                :label="option.label"
                :value="option.value"
              ></el-option>
            </template>
          </el-select>

          <el-tree-select
            v-else-if="isComponentType(column, 'SelectTree')"
            v-bind="componentProps(column)"
            v-model="searchValue"
            :disabled="expanded"
            style="width: 100%"
            @on-change="$nextTick(doSearch)"
          ></el-tree-select>

          <!-- 日期选择 -->
          <el-date-picker
            class="im-date-picker"
            v-else-if="isComponentType(column, 'DatePicker')"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            v-bind="componentProps(column)"
            v-model="searchValue"
            :disabled="expanded"
            style="width: 100%"
            @change="$nextTick(doSearch)"
          ></el-date-picker>

          <DateRange
            v-else-if="isComponentType(column, 'DateRange')"
            :range-options="column.component.options"
            v-model="searchValue"
            clearable
            @update:operator="
              operator => {
                column.component.operator = operator;
              }
            "
          ></DateRange>

          <el-button
            v-if="!expanded"
            class="expand-btn"
            @click="expanded = true"
          >
            <el-icon style="margin-right: 5px">
              <CirclePlusFilled></CirclePlusFilled>
            </el-icon>
            {{ expandBtn }}
          </el-button>

          <el-button v-else class="expand-btn" @click="expanded = false">
            <el-icon style="margin-right: 5px">
              <RemoveFilled></RemoveFilled>
            </el-icon>
            {{ collapseBtn }}
          </el-button>
        </div>

        <slot name="searchBtn">
          <el-button
            class="query-btn"
            type="primary"
            @click="doSearch"
            style="margin-left: 10px"
            :style="searchBtnStyle || {}"
          >
            <el-icon style="margin-right: 5px">
              <Search></Search>
            </el-icon>
            {{ searchBtn }}
          </el-button>
        </slot>
        <slot name="resetBtn">
          <el-button
            v-if="resetable"
            class="query-btn"
            @click="doReset"
            style="margin-left: 10px"
            :style="resetBtnStyle || {}"
          >
            <el-icon style="margin-right: 5px">
              <Refresh></Refresh>
            </el-icon>
            {{ resetBtn }}
          </el-button>
        </slot>
      </div>
      <slot name="right"></slot>
    </div>

    <div v-if="expanded" class="search-form-wrapper">
      <el-form
        ref=""
        class="search-form el-card"
        :model="{}"
        :inline="inline"
        :label-width="labelWidth"
        :label-position="labelPosition as any"
        :label-suffix="labelSuffix"
      >
        <el-row class="layout-align-row">
          <el-col
            v-for="(conditionColumn, index) in visibleConditionColumns"
            :key="index"
            class="form-align-column"
            :span="getColumnSpan(conditionColumn.column)"
            :style="getColumnStyle(conditionColumn.column)"
            v-show="checkRowLimit(index) && checkState(conditionColumn.column)"
          >
            <search-form-item
              :column="conditionColumn.column"
              :index="index"
              :fit="fit"
              :model="conditionColumn.model"
            ></search-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-drawer append-to-body :size="400" v-model="visible" title="设置">
        <div
          style="max-height: 80vh; overflow: auto; transform: translateY(-20px)"
        >
          <el-form label-width="120px">
            <el-form-item label="每行列数">
              <el-input-number
                v-model="formConfig.colNumPerRow"
                placeholder="设置列数"
                :min="1"
              ></el-input-number>
            </el-form-item>
            <el-form-item label="最大显示行数">
              <el-input-number
                v-model="formConfig.limitRowNum"
                :min="1"
                placeholder="设置列数"
              ></el-input-number>
            </el-form-item>

            <el-form-item label="条件字段">
              <div style="width: 100%">
                <el-checkbox
                  :indeterminate="isIndeterminate"
                  v-model="isCheckAll"
                  @change="handleCheckAllChange"
                  >全选(可拖拽排序)
                </el-checkbox>
                <div style="margin: 5px 0"></div>
                <el-checkbox-group
                  v-model="visibleConditionFields"
                  :min="1"
                  @change="handleFieldCheckedChange"
                >
                  <sortable
                    v-model="conditionColumns"
                    style="display: flex; flex-direction: column"
                  >
                    <template #item="{ item }">
                      <el-checkbox
                        class="checkbox-drag"
                        :label="item.column.field"
                        >{{ item.column.name }}
                      </el-checkbox>
                    </template>
                  </sortable>
                </el-checkbox-group>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </el-drawer>

      <template v-if="$slots.control">
        <slot
          name="control"
          v-bind="{
            config: formConfig,
            columns: conditionColumns,
            fieldKeys: visibleConditionFields
          }"
        ></slot>
      </template>
      <template v-else>
        <el-button
          circle
          size="small"
          title="条件设置"
          style="
            position: absolute;
            right: 10px;
            top: 12px; /*transform: translateY(-50%)*/
          "
          @click="visible = true"
        >
          <el-icon>
            <Setting></Setting>
          </el-icon>
        </el-button>
      </template>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  checkState,
  isRenderOption,
  isComponentType,
  getComponentProps
} from "@/components/ItsmCommon/util/form";
import SearchFormItem from "./SearchFormItem.vue";
import {
  CirclePlusFilled,
  RemoveFilled,
  Refresh,
  Setting,
  Search
} from "@element-plus/icons-vue";
import {
  computed,
  onBeforeMount,
  reactive,
  toRefs,
  useSlots,
  PropType,
  watch,
  CSSProperties,
  ref,
  nextTick,
  onMounted
} from "vue";
import Sortable from "@/components/ItsmCommon/sortable/Sortable.vue";
import DateRange from "@/components/ItsmCommon/form/components/DateRange.vue";

const defaultColumn = {
  component: "Input"
};
const emits = defineEmits([
  "on-search",
  "on-reset",
  "on-update-row-num",
  "on-expanded-change",
  "on-height-change"
]);
const $slots = useSlots(); // defineSlots();
const searchFormEl = ref<HTMLDivElement>();
const props = defineProps({
  /*高度*/
  baseHeight: {
    type: Number,
    default: 32
  },
  // 支持通过组件注入默认值对象（键值对）
  initValues: Object,
  /** 是否默认展开,如果默认展开会手动触发一次查询*/
  defaultExpanded: Boolean,
  /** 当展开时是否自动查询 */
  searchOnExpand: Boolean,
  /** 当重置时清空所有值，如果为true则按默认值重置 */
  defaultValuesOnReset: Boolean,
  /** 字段列表 */
  columns: Array as PropType<any[]>,
  /** 字段选择宽度 */
  selectWidth: {
    type: String,
    default: "120px"
  },
  /** 搜索域样式 */
  searchStyle: Object,
  /** 强制使用文本域 */
  useInputOnSearch: Boolean,
  /** 是否自动去除前后空格 */
  autoTrimStringValue: {
    type: Boolean,
    default: true
  },
  /** 是否显示重置按钮-默认显示*/
  resetable: {
    type: Boolean,
    default: true
  },
  /** 重置时是否触发搜索-默认true */
  searchOnReset: {
    type: Boolean,
    default: true
  },
  /** 仅仅以展开模式显示表单查询条件 */
  onlyExpandMode: Boolean,

  // 每行字段数量
  colNumPerRow: {
    type: Number,
    default: 4
  },
  // 每列高度
  colHeight: Number,
  // 限制行数（最大行数）
  limitRowNum: Number,
  // 自适应
  fit: Boolean,
  // 行内表单
  inline: Boolean,
  labelWidth: {
    type: String,
    default: "120px"
  },
  labelPosition: {
    type: String,
    default: "right"
  },
  labelSuffix: String,

  /** 统一配置项 */
  options: Object,

  /** 默认查询条件*/
  defaultField: String,

  /** 默认值 */
  defaultValue: [String, Number, Array, Object],

  /**搜索按钮样式*/
  searchBtnStyle: Object as PropType<CSSProperties>,
  /**重置按钮样式*/
  resetBtnStyle: Object as PropType<CSSProperties>
});

const searchMainStyle = computed((): CSSProperties => {
  let baseHeight = props.baseHeight || 32;
  if (baseHeight < 20) {
    baseHeight = 20;
  }
  return {
    "--search-form-height": `${baseHeight}px`
  };
});

const state = reactive({
  fuzzy: "fuzzy",
  numberOps: "",
  columnField: null,
  column: <any>defaultColumn,
  searchValue: null,

  // 折叠状态
  expanded: false,
  configOptions: {
    /** 统一搜索占位提示 */
    placeholder: "请输入关键字检索",
    searchBtn: "检索",
    resetBtn: "重置",
    expandBtn: "展开",
    collapseBtn: "收起"
  },

  formConfig: {
    colNumPerRow: 4,
    limitRowNum: 2
  },

  visible: false,
  conditionColumns: [],
  isIndeterminate: false,
  isCheckAll: true,
  visibleConditionFields: []
});

const {
  fuzzy,
  numberOps,
  column,
  expanded,
  searchValue,
  columnField,
  visible,
  formConfig,
  isIndeterminate,
  isCheckAll,
  conditionColumns,
  visibleConditionFields
} = toRefs(state);

// const column = computed(() => {
//   return state.column;
// });

const searchColumns = computed((): any[] => {
  return (props.columns || []).filter(column => {
    let disableSearch = column?.component?.disableSearch;
    return !disableSearch;
  });
});

const visibleConditionColumns = computed(() => {
  return state.conditionColumns.filter(conditionColumn =>
    state.visibleConditionFields.includes(conditionColumn.column.field)
  );
});

const defaultProps = computed(() => {
  let options = props.options || {};
  return Object.assign(
    {
      clearable: true
    },
    state.configOptions,
    options
  );
});

const searchBtn = computed(() => {
  return defaultProps.value.searchBtn || "检索";
});

const resetBtn = computed(() => {
  return defaultProps.value.resetBtn || "重置";
});

const expandBtn = computed(() => {
  return defaultProps.value.expandBtn || "展开";
});

const collapseBtn = computed(() => {
  return defaultProps.value.collapseBtn || "收起";
});

const conditionRowNum = computed(() => {
  let count = state.visibleConditionFields.length;
  let { limitRowNum, colNumPerRow = 2 } = state.formConfig;
  let n = count % colNumPerRow;
  let row = n == 0 ? count / colNumPerRow : (count - n) / colNumPerRow + 1;
  row = Math.min(row, limitRowNum);
  return state.expanded ? row : 0;
});

const init = () => {
  let { colNumPerRow, limitRowNum } = props;
  if (colNumPerRow > 0) {
    state.formConfig.colNumPerRow = colNumPerRow;
  }
  if (limitRowNum > 0) {
    state.formConfig.limitRowNum = limitRowNum;
  }
};

const initColumns = () => {
  state.conditionColumns = searchColumns.value.map(column => {
    let { component, field } = column;
    let { type, fuzzyable, operator, props } = component;
    return {
      visible: true,
      column,
      model: {
        field,
        type,
        fuzzyable: !!fuzzyable,
        operator,
        value: getColumnDefaultValue(column)
      }
    };
  });
  handleCheckAllChange(true);
};

const changeSearchColumn = column => {
  state.column = column;
  state.searchValue = props.defaultValuesOnReset
    ? getColumnDefaultValue(column)
    : null;
  // state.fuzzy = "fuzzy";
  state.numberOps = null;
};

const fuzzyable = column => {
  return column?.component?.fuzzyable;
};

const componentProps = column => {
  let { value, ...props } = getComponentProps(column);
  if (props.type == "textarea") {
    props.type = "text";
  }
  // 替换动态占位信息
  let componentProps = Object.assign(
    {},
    defaultProps.value,
    { modelValue: value },
    props
  );
  if (
    componentProps.placeholder &&
    componentProps.placeholder.indexOf("${name}") > -1
  ) {
    componentProps.placeholder = componentProps.placeholder.replace(
      "${name}",
      column.name || "关键字"
    );
  }
  return componentProps;
};

const getColumnSpan = column => {
  let { colNumPerRow = 2 } = state.formConfig;
  let columnSpan = 24 / colNumPerRow;
  let { span } = column.component || {};
  return span || columnSpan;
};

const getColumnStyle = (column): CSSProperties => {
  let { colNumPerRow = 2 } = state.formConfig;
  let style: CSSProperties = {
    minHeight: `${props.colHeight || 52}px`
  };
  if (24 % colNumPerRow > 0) {
    style.width = (100 / colNumPerRow).toFixed(2) + "%";
  }
  return style;
};

const checkRowLimit = index => {
  let { limitRowNum, colNumPerRow = 2 } = state.formConfig;
  if (!limitRowNum || limitRowNum == 0) {
    return true;
  }
  return index < limitRowNum * colNumPerRow;
};

/** 检索事件 */
const doSearch = () => {
  let conditionModels = [];
  let queryModel = {};

  try {
    if (state.expanded) {
      conditionModels = visibleConditionColumns.value
        .map(conditionColumn => conditionColumn.model)
        .filter(
          conditionModel =>
            conditionModel.value != null &&
            conditionModel.value != "" &&
            (!Array.isArray(conditionModel.value) ||
              conditionModel.value.length > 0)
        );
      /**解决支持空格输入但绑定值去除空格只能拷贝备份 */
      conditionModels = JSON.parse(JSON.stringify(conditionModels));
      for (let conditionModel of conditionModels) {
        let value = conditionModel.value;
        delete conditionModel.fuzzyable;
        if (props.autoTrimStringValue && typeof value == "string") {
          value = value.trim();
          if (value) {
            queryModel[conditionModel.field] = value;
            conditionModel.value = value;
          }
        } else {
          queryModel[conditionModel.field] = value;
        }
      }
    } else {
      let { component, field } = state.column;
      let { type, fuzzyable, operator } = component;

      if (state.searchValue) {
        let value = state.searchValue;
        if (props.autoTrimStringValue && typeof state.searchValue == "string") {
          value = value.trim();
        }

        let conditionModel = {
          type,
          field,
          // fuzzyable: !!fuzzyable,
          operator: fuzzyable ? state.fuzzy : operator,
          value
        };

        if (state.numberOps) {
          conditionModel.operator = state.numberOps;
        }

        conditionModels = [conditionModel];
        queryModel[field] = state.searchValue;
      }
    }
  } catch (e) {
    console.error(e);
  }

  emits("on-search", [...conditionModels], queryModel);
};

const getConditionModels = () => {
  let conditionModels = visibleConditionColumns.value
    .map(conditionColumn => conditionColumn.model)
    .filter(
      conditionModel =>
        conditionModel.value != null && conditionModel.value != ""
    );
  /**解决支持空格输入但绑定值去除空格只能拷贝备份 */
  conditionModels = JSON.parse(JSON.stringify(conditionModels));
  for (let conditionModel of conditionModels) {
    let value = conditionModel.value;
    if (props.autoTrimStringValue && typeof value == "string") {
      value = value.trim();
      if (value) {
        conditionModel.value = value;
      }
    }
  }
  return conditionModels;
};

const getColumnDefaultValue = column => {
  let { component, field } = column;
  if (props.initValues) {
    return props.initValues[field];
  }
  return component?.props?.value || null;
};

/** 重置 */
const doReset = () => {
  state.searchValue = props.defaultValuesOnReset
    ? getColumnDefaultValue(state.column)
    : null;
  state.conditionColumns &&
    state.conditionColumns.forEach(conditionColumn => {
      let { model, column } = conditionColumn;
      if (model) {
        model.value = props.defaultValuesOnReset
          ? getColumnDefaultValue(column)
          : null;
      }
    });
  emits("on-reset");
  if (props.searchOnReset) {
    doSearch();
  }
};

const handleCheckAllChange = val => {
  state.visibleConditionFields = val
    ? state.conditionColumns.map(
        conditionColumn => conditionColumn.column.field
      )
    : [];
  state.isIndeterminate = false;
  if (!val && state.conditionColumns.length > 1) {
    state.isIndeterminate = true;
    state.visibleConditionFields = [state.conditionColumns[0].column.field];
  }
};

/**字段选择变化*/
const handleFieldCheckedChange = value => {
  let checkedCount = value.length;
  state.isCheckAll = checkedCount === state.conditionColumns.length;
  state.isIndeterminate =
    checkedCount > 0 && checkedCount < state.conditionColumns.length;
};

const checkedDefaultColumn = () => {
  if (searchColumns.value) {
    if (props.defaultField) {
      state.column = searchColumns.value.find(
        searchColumn => searchColumn.field == props.defaultField
      );
      state.columnField = state.column?.field;
    } else {
      state.column = searchColumns.value[0];
      state.columnField = state.column?.field;
    }
    if (props.defaultValue) {
      state.searchValue = props.defaultValue;
    }
  }
};

/***
 * 更新搜索字段和值
 *
 * @param field
 * @param value
 */
const updateSearch = (searchField, searchValue, autoSearch) => {
  if (searchColumns.value) {
    if (searchField) {
      state.column = searchColumns.value.find(
        searchColumn => searchColumn.field == searchField
      );
      state.columnField = state.column?.field;
    } else {
      state.column = searchColumns.value[0];
      state.columnField = state.column?.field;
    }
    state.searchValue = searchValue;
    if (autoSearch) {
      doSearch();
    }
  }
};

const triggerHeightChange = () => {
  if (!searchFormEl.value) return;
  nextTick(() => {
    let { height } = searchFormEl.value.getBoundingClientRect();
    emits("on-height-change", height);
  });
};

onBeforeMount(() => {
  init();
});

onMounted(() => {
  nextTick(triggerHeightChange);
});

watch(
  () => searchColumns.value,
  val => {
    if (val) {
      checkedDefaultColumn();
      initColumns();
      // 如果默认展开触发一次查询
      nextTick(() => {
        if (props.defaultExpanded) {
          if (val.length > 0) {
            doSearch();
          }
        }
      });
    }
  },
  { immediate: true }
);
watch(
  () => props.colNumPerRow,
  () => {
    init();
  }
);
watch(
  () => props.limitRowNum,
  () => {
    init();
  }
);
watch(
  () => props.defaultField,
  () => {
    checkedDefaultColumn();
  }
);

watch(
  () => conditionRowNum.value,
  val => {
    emits("on-update-row-num", val);
    emits("on-expanded-change", state.expanded, val);
    triggerHeightChange();
  },
  {
    immediate: true
  }
);

watch(
  () => state.expanded,
  val => {
    emits("on-expanded-change", val, conditionRowNum.value);
    triggerHeightChange();
    if (val && props.searchOnExpand) {
      doSearch();
    }
  }
);

watch(
  () => props.onlyExpandMode,
  val => {
    if (val) {
      state.expanded = val;
    }
  },
  {
    immediate: true
  }
);

// 默认展开
watch(
  () => props.defaultExpanded,
  val => {
    state.expanded = val;
  },
  { immediate: true }
);

defineExpose({
  updateSearch,
  getConditionModels
});
</script>

<style lang="scss" scoped>
.im-search-form {
  --search-form-height: 32px;

  .search-area {
    display: flex;
    align-items: center;

    .search-area-input {
      height: calc(var(--search-form-height));
      width: 100%;
      display: flex;
      align-items: center;
      border: 1px solid #dcdfe6 !important;
      border-radius: 4px;

      :deep(.el-input__wrapper) {
        box-shadow: unset !important;

        :hover {
          box-shadow: unset !important;
        }
      }

      :deep(.el-select__wrapper) {
        box-shadow: unset !important;
        background-color: unset !important;

        :hover {
          box-shadow: unset !important;
        }
      }

      :deep(input) {
        border: unset !important;
      }

      :deep(.el-input__inner) {
        border: unset !important;
      }

      :deep(textarea) {
        border: unset !important;
        resize: none;
      }

      .expand-btn {
        border: unset;
        border-radius: unset !important;
        border-left: 1px solid #dcdfe6;
        margin-top: -1px;
        margin-bottom: -1px;
        padding: 12px 15px;
        height: calc(var(--search-form-height) - 2px);

        &:hover {
          background: unset !important;
        }
      }
    }

    width: 100%;

    .column-select {
      margin-right: -1px;
    }

    .im-date-picker {
      border-radius: unset !important;
    }

    :deep(.el-input-group__append) {
      border-radius: unset !important;
      border: unset;
      border-left: 1px solid #dcdfe6;
      height: calc(var(--search-form-height) - 2px);
    }

    :deep(input) {
      border-radius: unset !important;
      height: calc(var(--search-form-height) - 4px) !important;
    }

    .query-btn {
      // border-radius: unset;
      margin-top: -1px;
      margin-bottom: -1px;
      padding: 12px 15px;
      height: calc(var(--search-form-height));
      line-height: calc(var(--search-form-height));
      width: 80px;
    }

    .prpendable-number {
      :deep(.el-input-group__prepend) {
        border: unset !important;
        background: unset !important;
      }
    }
  }

  .search-form-wrapper {
    position: relative;

    .search-form {
      margin-top: 10px;
      padding-top: 10px;
      padding-right: 50px;

      :deep(.el-form-item) {
        margin-bottom: 8px !important;

        .el-form-item__label {
          font-size: 13px !important;
        }
      }
    }
  }

  .checkbox-drag:hover {
    cursor: move;
  }

  :deep(.search-form .el-input__inner) {
    //height: 36px !important;
    //line-height: 36px !important;
  }

  :deep(.search-form .el-form-item__label) {
    line-height: 36px !important;
    align-items: center;
  }

  :deep(.search-area .el-input-group__append) {
    box-shadow: unset !important;
  }
}
</style>
