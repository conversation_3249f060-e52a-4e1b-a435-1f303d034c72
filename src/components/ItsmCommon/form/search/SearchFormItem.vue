<template>
  <el-form-item class="search-form-item" :label="column.name">
    <template #label>
      <div :title="column.name" class="form-item-label">
        {{ column.name }}
      </div>
    </template>

    <el-input
      v-if="isComponentType(column, 'Input', ['input'])"
      v-bind="componentProps(column)"
      v-model="model.value"
      :autosize="false"
      :rows="1"
    >
      <template v-if="operators.length > 0" #prepend>
        <el-select
          v-model="model.operator"
          placeholder="运算符"
          style="min-width: 75px"
        >
          <el-option
            v-for="(option, index) in operators"
            :key="index"
            v-bind="option"
          ></el-option>
        </el-select>
      </template>
      <template v-if="model.fuzzyable" #append>
        <el-checkbox
          v-model="model.operator"
          true-label="fuzzy"
          false-label="exact"
          >模糊</el-checkbox
        >
      </template>
    </el-input>
    <el-input
      v-else-if="isComponentType(column, 'Number')"
      class="prpendable-number"
      type="number"
      v-bind="componentProps(column)"
      v-model.number="model.value"
    >
      <template v-if="column.component.operators?.length > 0" #prepend>
        <el-select
          v-model="model.operator"
          placeholder="运算符"
          style="width: 100px"
        >
          <el-option
            v-for="(option, index) in column.component.operators"
            :key="index"
            v-bind="option"
          ></el-option>
        </el-select>
      </template>
    </el-input>
    <el-select
      v-else-if="isComponentType(column, 'Select')"
      v-bind="componentProps(column)"
      v-model="model.value"
      filterable
      style="width: 100%"
    >
      <template v-for="(option, index) in enumOptions" :key="index">
        <el-option
          v-if="isRenderOption(option)"
          :label="option.label"
          :value="option.value"
        ></el-option>
      </template>
    </el-select>
    <el-tree-select
      v-else-if="isComponentType(column, 'SelectTree')"
      v-bind="componentProps(column)"
      v-model="model.value"
    ></el-tree-select>
    <!--    <im-time-select-->
    <!--      v-else-if="isComponentType(column, 'TimeSelect')"-->
    <!--      v-bind="componentProps(column)"-->
    <!--      v-model="model.value"-->
    <!--    >-->
    <!--    </im-time-select>-->
    <el-checkbox-group
      v-else-if="isComponentType(column, 'Checkbox')"
      v-bind="componentProps(column)"
      v-model="model.value"
    >
      <el-checkbox
        :class="{ 'checkbox-button': isCheckboxButton }"
        v-for="(option, index) in enumOptions"
        :key="index"
        border
        v-bind="option"
      ></el-checkbox>
    </el-checkbox-group>
    <el-radio-group
      v-else-if="isComponentType(column, 'Radio')"
      v-bind="componentProps(column)"
      v-model="model.value"
    >
      <el-radio-button
        v-for="(option, index) in enumOptions"
        :key="index"
        :label="option.value"
        >{{ option.label }}</el-radio-button
      >
    </el-radio-group>
    <el-date-picker
      v-else-if="isComponentType(column, 'DatePicker')"
      v-bind="getDatePickerProps(column)"
      v-model="model.value"
      clearable
      style="max-width: 100%"
    ></el-date-picker>

    <!-- 最近时间或最近时间范围 -->
    <DateRange
      v-else-if="isComponentType(column, 'DateRange')"
      :range-options="enumOptions"
      :picker-props="componentProps(column)"
      clearable
      v-model="model.value"
      @update:operator="
        operator => {
          model.operator = operator;
        }
      "
    ></DateRange>
  </el-form-item>
</template>

<script lang="ts" setup>
import { computed, onBeforeMount } from "vue";
import {
  getDateRange,
  isRenderOption,
  isComponentType,
  getComponentProps
} from "@/components/ItsmCommon/util/form";
import DateRange from "@/components/ItsmCommon/form/components/DateRange.vue";

const props = defineProps({
  column: Object,
  index: Number,
  model: Object,
  // 自适应
  fit: Boolean
});

// const externalParams = computed(() => {
//   return props.model;
// });

const defaultProps = computed(() => {
  let dp: any = {
    clearable: true
  };
  if (props.fit) {
    dp.style = {
      width: "100%"
    };
  }
  return dp;
});

const componentProps = column => {
  // 排除value(vue3中使用modelValue)
  let { value, ...props } = getComponentProps(column);
  // 替换动态占位信息
  let cProps = Object.assign(
    {},
    defaultProps.value,
    { modelValue: value },
    props
  );
  if (!cProps.placeholder) {
    if ((column.component.type || "Input") == "Input") {
      cProps.placeholder = "请输入";
    } else {
      cProps.placeholder = "请选择";
    }
  } else {
    if (cProps.placeholder.indexOf("${name}") > -1) {
      cProps.placeholder = cProps.placeholder.replace(
        "${name}",
        column.name || "关键字"
      );
    }
  }
  return cProps;
};

const getDatePickerProps = column => {
  let props = componentProps(column);
  // if (this.autoDateToRange) {
  //   let dateType = props.type;
  //   if (!dateType.endsWith("range")) {
  //     props.type = dateType + "range";
  //   }
  // }

  let options = column.component && column.component.options;
  if (Array.isArray(options) && options.length > 0) {
    // 是否范围
    let isDaterange =
      props.type == "datetimerange" ||
      props.type == "daterange" ||
      props.type == "monthrange";
    // 构建快捷选项
    Object.assign(props, {
      pickerOptions: {
        shortcuts: options.map(option => {
          return {
            text: option.label,
            onClick: picker => {
              picker.$emit("pick", getDateRange(option.value, isDaterange));
            }
          };
        })
      }
    });
  }
  return props;
};

// // 数组对象
// const arrayModel = field => {
//   let arr = externalParams.value[field];
//   if (!arr) {
//     externalParams[field] = [];
//   }
//   return externalParams.value;
// };

// /**日期对象*/
// const dateModel = column => {
//   if (column && column.component && column.component.props) {
//     let dateType = column.component.props.type;
//     if (
//       dateType == "datetimerange" ||
//       dateType == "daterange" ||
//       dateType == "monthrange"
//     ) {
//       return arrayModel(column.field);
//     }
//   }
//   return externalParams.value;
// };

const operators = computed(() => {
  let operators = props.column.component.operators;
  return Array.isArray(operators) ? operators : [];
});

const enumOptions = computed(() => {
  return props.column.component?.options || [];
  // .filter(option =>
  //   isRenderOption(option, externalParams.value)
  // );
});

const isCheckboxButton = computed(() => {
  if (isComponentType(props.column, "Checkbox")) {
    let { button } = props.column.component || {};
    return !!button;
  }
  return false;
});

// 处理初始化属性，比如checkbox value需要为[]不能为""或者null
onBeforeMount(() => {
  if (isComponentType(props.column, "Checkbox")) {
    if (!props.model?.value) {
      props.model.value = [];
    }
  }
});
</script>

<style lang="scss" scoped>
.search-form-item {
  .form-item-label {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    font-weight: bold;
  }
  .prpendable-number {
    :deep(.el-input-group__prepend) {
      background: unset !important;
    }
  }
  .checkbox-button {
    margin-right: 8px !important;
    margin-bottom: 5px !important;
    padding: 0 10px !important;
    &.is-checked {
      background: var(--el-color-primary) !important;
      :deep(.el-checkbox__label) {
        color: #fff !important;
      }
    }
    :deep(.el-checkbox__input) {
      display: none !important;
    }
    :deep(.el-checkbox__label) {
      padding-left: 0 !important;
    }
  }
}
</style>
