<template>
  <template v-if="selectType == 'tile'">
    <el-segmented
      v-model="state.dateRange"
      :options="rangeOptions"
      @change="handleChange"
    >
    </el-segmented>
    <el-date-picker
      v-model="state.dateRange"
      type="datetimerange"
      value-format="YYYY-MM-DD HH:mm:ss"
      format="YYYY-MM-DD HH:mm:ss"
      start-placeholder="选择开始"
      end-placeholder="选择结束"
      style="margin-left: 8px"
      v-bind="pickerProps || {}"
      :clearable="clearable"
      @change="handleChange"
    ></el-date-picker>
  </template>

  <template v-else>
    <el-popover
      ref="popoverRef"
      placement="bottom-start"
      :width="360"
      trigger="click"
    >
      <template #reference>
        <el-input
          ref="inputEl"
          v-model="state.dateRangeText"
          :disabled="disabled"
          readonly
          @click="state.visible = true"
          placeholder="请选择时间"
          class="popover-trigger-input"
        >
          <template v-if="clearable" #suffix>
            <el-icon v-if="state.dateRange">
              <CircleClose
                class="close-icon"
                style="cursor: pointer"
                @click="
                  e => {
                    e.stopPropagation();
                    state.dateRangeText = '';
                    state.dateRange = null;
                    emitsValue();
                  }
                "
              />
            </el-icon>
          </template>
        </el-input>
      </template>
      <el-form label-position="top">
        <el-form-item label="最近时间段">
          <el-select
            v-model="state.dateRange"
            style="width: 100%"
            :teleported="teleported"
            @change="handleChange"
          >
            <el-option
              v-for="(dateRange, index) in rangeOptions"
              :key="index"
              v-bind="dateRange"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="自定义时间范围">
          <el-input v-show="false" />
          <el-date-picker
            v-model="state.dateRange"
            type="datetimerange"
            value-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm:ss"
            :teleported="false"
            start-placeholder="选择开始"
            end-placeholder="选择结束"
            v-bind="datePickerProps"
            @change="handleChange"
          ></el-date-picker>
        </el-form-item>
      </el-form>
    </el-popover>
  </template>
</template>
<script setup lang="ts">
import { computed, PropType, reactive, ref, watch } from "vue";
import { CircleClose } from "@element-plus/icons-vue";
import { InputInstance, PopoverInstance } from "element-plus";

const emits = defineEmits([
  "on-change",
  "update:modelValue",
  "update:operator"
]);
const inputEl = ref<InputInstance>();
const popoverRef = ref<PopoverInstance>();
type DisplayType = "tile" | "popover";
const props = defineProps({
  teleported: {
    type: Boolean,
    default: true
  },
  // 选择展现方式（平铺/浮窗）
  selectType: {
    type: String as PropType<DisplayType>,
    default: "popover"
  },
  // 时间范围
  modelValue: {
    type: [Array, String] as PropType<string[] | string>,
    default: () => {
      return [];
    }
  },
  // 选项
  rangeOptions: {
    type: Array as PropType<any[]>,
    default: () => {
      return [];
    }
  },
  // 选择器配置
  pickerProps: {
    type: Object
  },
  clearable: Boolean,
  disabled: Boolean
});

const datePickerProps = computed(() => {
  let { value, modelValue, ...otherProps } = props.pickerProps || {};
  // exclude value modelValue
  return { ...otherProps };
});

const state = reactive({
  visible: false,
  dateRange: "",
  dateRangeSign: "",
  dateRangeText: "",
  dateRangeWith: 90
});

const updateDateRangeText = () => {
  if (Array.isArray(state.dateRange)) {
    state.dateRangeText = state.dateRange.join("~");
    state.dateRangeWith = 240;
  } else {
    let rangeOptions = props.rangeOptions || [];
    let dateRangeOption = rangeOptions.find(
      dateRangeOption => state.dateRange == dateRangeOption.value
    );
    state.dateRangeText = dateRangeOption
      ? dateRangeOption.label
      : state.dateRange || "";
    state.dateRangeWith = 90;
  }
};

const handleChange = val => {
  state.dateRange = val as any;
  if (props.selectType == "popover") {
    updateDateRangeText();
    // 触发点击关闭浮窗
    // inputEl.value.input.click();
    // state.visible = false;
    popoverRef.value.hide();
  }
  emitsValue();
};

const emitsValue = () => {
  emits(
    "update:operator",
    Array.isArray(state.dateRange) ? "rangeTime" : "latelyTime"
  );
  emits("update:modelValue", state.dateRange);
  emits("on-change", state.dateRange);
};

watch(
  () => props.modelValue,
  val => {
    state.dateRange = val as any;
    if (!val) {
      state.dateRangeText = null;
    } else {
      updateDateRangeText();
    }
  },
  { immediate: true }
);
</script>
<style scoped lang="scss">
.popover-trigger-input {
  .close-icon {
    display: none;
  }

  &:hover {
    .close-icon {
      display: block;
    }
  }
}
</style>
