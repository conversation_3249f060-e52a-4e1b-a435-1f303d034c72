<template>
  <im-table
    :size="size"
    center
    :data="data"
    :sortable="sortable"
    :columns="defineColumns"
  >
    <template #hidden="{ row }">
      <el-checkbox v-model="row.hidden"></el-checkbox>
    </template>
    <!--    <template #align="{ row }">-->
    <!--      <el-select-->
    <!--        size="default"-->
    <!--        placeholder="请选择"-->
    <!--        v-model="row.align"-->
    <!--        clearable-->
    <!--      >-->
    <!--        <el-option label="居左" value="left"></el-option>-->
    <!--        <el-option label="居中" value="center"></el-option>-->
    <!--        <el-option label="居右" value="right"></el-option>-->
    <!--      </el-select>-->
    <!--    </template>-->
    <template #fixed="{ row }">
      <!--      <el-select-->
      <!--        size="default"-->
      <!--        placeholder="请选择"-->
      <!--        v-model="row.fixed"-->
      <!--        clearable-->
      <!--      >-->
      <!--        <el-option label="居左" value="left"></el-option>-->
      <!--        <el-option label="居右" value="right"></el-option>-->
      <!--      </el-select>-->
      <el-checkbox
        :size="size"
        :model-value="!!row.fixed"
        @change="val => handleFixed(val, row)"
      ></el-checkbox>
    </template>
    <template #filterable="{ row }">
      <span
        v-if="!row.prop || row.meta?.filterable == 'disabled'"
        style="opacity: 0.5"
        >-</span
      >
      <el-checkbox
        v-else
        :size="size"
        v-model="row.filterable"
        @change="handleFilterableChange(row)"
      ></el-checkbox>
    </template>
    <template #sortable="{ row }">
      <!--      <el-select-->
      <!--        v-if="row.prop"-->
      <!--        size="default"-->
      <!--        placeholder="请选择"-->
      <!--        v-model="row.sortable"-->
      <!--        clearable-->
      <!--      >-->
      <!--        <el-option label="本地" :value="true"></el-option>-->
      <!--        <el-option label="远程" value="custom"></el-option>-->
      <!--      </el-select>-->
      <span
        v-if="!row.prop || row.meta?.sortable == 'disabled'"
        style="opacity: 0.5"
        >-</span
      >
      <el-checkbox
        v-else
        :size="size"
        v-model="row.__sortable"
        @change="val => handleSortableChange(val, row)"
      ></el-checkbox>
    </template>
  </im-table>

  <!--  <template v-if="isDevelopment()">-->
  <!--    <textarea style="width: 100%; min-height: 360px">-->
  <!--      {{ JSON.stringify(data, null, 4) }}-->
  <!--    </textarea>-->
  <!--  </template>-->
</template>
<script lang="ts" setup>
import { ImTableColumnProps } from "./types";
import { nextTick, PropType } from "vue";

defineProps({
  size: {
    type: String as PropType<"large" | "default" | "small">,
    default: "small"
  },
  data: {
    type: Array as PropType<ImTableColumnProps<any>[]>,
    required: true
  },
  sortable: Boolean
});
const defineColumns: ImTableColumnProps<any>[] = [
  {
    label: "名称",
    prop: "label"
  },
  {
    label: "隐藏",
    prop: "hidden",
    width: "150px"
  },
  // {
  //   label: "位置",
  //   prop: "align"
  // },
  {
    label: "冻结",
    width: "150px",
    prop: "fixed"
  },
  {
    label: "过滤",
    width: "150px",
    prop: "filterable"
  },
  {
    label: "排序",
    width: "150px",
    prop: "sortable"
  }
];
const handleFilterableChange = (column: ImTableColumnProps<any>) => {
  if (column.meta) {
    column.meta.filterable = column.filterable;
  }
};
const handleFixed = (val, row) => {
  if (!val) {
    if (typeof row.fixed == "string") {
      row.__fixed_label = row.fixed;
    }
    row.fixed = false;
  } else {
    row.fixed = row.__fixed_label || true;
  }
};

const handleSortableChange = (val, row) => {
  if (!val) {
    row.sortable = false;
  } else {
    row.sortable = row.__sortable_label || true;
  }
  console.log("handleSortableChange", row);
};
</script>
