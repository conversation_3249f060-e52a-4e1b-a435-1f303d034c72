import type {
  FormItemProps,
  PaginationProps,
  PopoverProps,
  TableColumnCtx,
  TableInstance,
  TableProps
} from "element-plus";
import type { Component, CSSProperties } from "vue";
import { ComputedRef } from "@vue/reactivity";
import { JSX } from "vue/jsx-runtime";
import { SortableOption } from "@/components/ItsmCommon";
// 对齐方式
export type Align = "right" | "left" | "center";
export type Size = "small" | "default" | "large";
export interface ImTablePaginationProps {
  /*extends PaginationProps*/
  // 是否隐藏
  hide?: boolean;
  // // 位置
  align?: "right" | "left" | "center";
  // 没有数据时隐藏
  hideOnEmptyData?: boolean;
  // 是否内存分页
  memory?: boolean;
  // 分页background属性
  background?: boolean;
  // 当前页
  currentPage?: number;
  // 总数
  total?: number;
  // 每页条数
  pageSize?: number;
  // 分页size选项列表
  pageSizes?: number[];
  // 分页布局
  layout?: string;
  // // 分页属性其他属性配置
  props?: PaginationProps;
}

/** 表格内部计算状态判断各个模块是否显示 */
export interface TableInternalState {
  // 是否增强模式
  enhanced: boolean;
  // 是否显示alert
  showAlert: boolean;
  // 是否显示toolbar
  showToolbar: boolean;
  // 是否显示分页
  showPage: boolean;
  // 是否支持刷新
  reloadBar: boolean;
  // 是否支持设置
  settingBar: boolean;
  // 分页栏样式
  paginationStyle: CSSProperties;
  // 是否内存分页
  memoryPage: boolean;
  // 是否显示操作列
  showOperator: boolean;
  // 使用request
  useRequest: boolean;
  // 是否自动请求
  autoRequest: boolean;
  // 是否支持字段排序
  columnSortable: boolean;
  // 排序配置项
  columnSortableOption: SortableOption;
}

/**
 * 表格实例暴露方法
 */
export interface ImTableInstance extends TableInstance {
  // 获取table实例
  getTable: () => TableInstance;
  // 获取字段列表(默认不包含隐藏字段，includeHidden为true可以返回)
  getColumns: (includeHidden: boolean) => ImTableColumnProps<any>[];
  // 默认列对齐方式
  defaultAlign: () => string;
  // 获取当前索引开始位置
  getOffsetIndex: () => number;
  // 获取设置的size
  getSize: () => string;
  // table选项信息
  tableStates: TableInternalState;
  // 当前选择行记录
  checkedRows: any[];
  // 工具栏
  toolbarState: object;
  // 刷新
  reload: () => ImTableInstance;
  // 清除当前内置过滤
  clearCurrentFilter: () => ImTableInstance;
  // 支持外部调用更新过滤列的options,适合列动态构建的场景
  setFilterOptions: (columnKey: string, options: any[]) => ImTableInstance;
  // 获取表格数据
  getData: () => any[];
  // 查询页码数据
  loadPage: (page: number) => ImTableInstance;
  // 返回当前页
  currentPage: (page: number) => ImTableInstance;
  // 获取所有过滤配置（字段+values）
  getFilterValues: () => HeaderFilterValue[];
  // 清除所有过滤
  clearAllFilters: () => void;
}

// 插槽类型声明
export interface ImToolbarSlot {
  checkedRows: any[];
  instance: ImTableInstance;
  pagination: ImTablePaginationProps;
  toolbarState: {
    [key: string]: any;
  };
  size: Size;
}

export interface TableToolbar {
  // 刷新
  reload?: boolean | Component;
  // 设置
  setting?: boolean | Component;
  // 初始化toolbar状态对象非必须使用
  initialState?: {
    [key: string]: any;
  };
}

export interface HeaderFilterValue {
  prop: string;
  values: any[];
}

/**
 * 表格上下文信息
 */
export interface TableContext {
  filterUpdateCount: number;
  currentSort: Object;
  currentFilter: Object;
  getLocalColumnOptions: (column: ImTableColumnProps<any>) => any[];
  doFilterLocal: (
    values: any[] | null,
    column: ImTableColumnProps<any>
  ) => void;
  doFilterEmits: (
    params: any,
    value: any,
    column: ImTableColumnProps<any>
  ) => void;
  getHeaderFilterValues: () => HeaderFilterValue[];
  addHeaderFilterValue: (headerFilter: HeaderFilterValue) => void;
  removeHeaderFilterValue: (headerFilter: HeaderFilterValue) => void;
  clearAllFilters: () => void;
}

type StorageMode = "local" | "remote";

/**
 * 针对持久化字段存储设计提供api
 */
export interface TableColumnStorage {
  // 存储模式: 本地(浏览器)/远程（服务端后台）
  mode: StorageMode;
  // 表格编码(唯一)
  tableCode: string;
  // 个性化实例
  instance?: string;
  // 获取全量字段
  getTableSet?: (code: string, instance: string) => Promise<any>;
  // 字段变化时保存方法
  saveTableSet?: (tableSet: any) => Promise<any>;
  // 支持字段转化
  mapColumnFn?: (col: ImTableColumnProps<any>) => ImTableColumnProps<any>;
}

export interface ColumnsState {
  id: string;
  hidden: boolean;
  fixed: string | boolean;
  filterable: string | boolean;
  sortable: boolean;
}

export interface CustomTableSet {
  pagination: {
    align: Align;
  };
  defaultAlign: Align;
  columnsStates: ColumnsState[];
}

export const createColumnStorage = (
  tableCode,
  mode: StorageMode = "remote",
  mapColumnFn?: (col: ImTableColumnProps<any>) => ImTableColumnProps<any>,
  get?: (code: string, instance: string) => Promise<any>,
  save?: (tableSet: any) => Promise<any>
): TableColumnStorage => {
  return {
    tableCode,
    mode,
    mapColumnFn,
    getTableSet: get,
    saveTableSet: save
  };
};

export type { TableProps, PaginationProps, TableColumnCtx, FormItemProps };

export const EnumTypes = [
  "Select",
  "select",
  "el-select",
  "Radio",
  "radio",
  "el-radio",
  "Checkbox",
  "checkbox",
  "el-checkbox"
] as const;
export type EnumType = (typeof EnumTypes)[number];
export type InputType =
  | EnumType
  | "Input"
  | "input"
  | "tree"
  | "SelectTree"
  | "el-tree-select"
  | "DatePicker"
  | "date"
  | "TimePicker"
  | "time";

type OptionsPromiseFn = () => Promise<any[]>;
type FilterMode = "local" | "custom";

export interface ColumnMetaProps {
  type?:
    | "number"
    | "textarea"
    | "datetime"
    | "datetimerange"
    | "daterange"
    | "timerange"
    | string;
  multiple?: boolean;
  collapseTags?: boolean;
  maxCollapseTags?: number;
  placeholder?: string;
  startPlaceholder?: string;
  endPlaceholder?: string;
  readonly?: boolean;
  disabled?: boolean;
  value?: any;
  modelValue?: any;
  formatter?: string;
  format?: string;
  valueFormat?: string;
  max?: number;
  min?: number;
  style?: CSSProperties;
}

export interface ColumnMeta {
  // 编辑态/查询条件展现的组件
  type?: InputType | string | Component;
  // 组件的属性信息
  props?: ColumnMetaProps;
  // 是否支持过滤（如果为disabled禁止设置修改）
  filterable?: boolean | "disabled";
  // 是否支持排序（如果为disabled禁止设置修改）
  sortable?: boolean | "disabled";
  // 过滤类型
  filterType?: InputType | string | Component;
  // 过滤模式
  filterMode?: FilterMode;
  // 静态数据/响应式数据/无参函数(异步)
  options?: any[] | OptionsPromiseFn | ComputedRef<any[]>;
  // 如果不设置默认cached
  optionsNoCached?: boolean;
  // 表单选项属性
  formItemProps?: FormItemProps;
  // 浮窗属性
  popoverProps?: PopoverProps;
  popoverStyle?: CSSProperties;
  hiddenPopoverOnFilter?: boolean;
  // 单位
  unit?: string;
  // 个性化信息
  customParams?: any;
}

type FilterMethods<T> = (value: any, row: T, column: TableColumnCtx<T>) => void;
type Fixed = "left" | "right";
export interface ImTableColumnProps<T> {
  id?: string;
  realWidth?: number;
  type?: string;
  label?: string;
  className?: string;
  labelClassName?: string;
  property?: string;
  prop?: string;
  width?: string | number;
  minWidth?: string | number;
  sortable?: boolean | string;
  sortMethod?: (a: T, b: T) => number;
  sortBy?: string | ((row: T, index: number) => string) | string[];
  resizable?: boolean;
  columnKey?: string;
  rawColumnKey?: string;
  align?: Align;
  headerAlign?: Align;
  showOverflowTooltip?: boolean;
  fixed?: boolean | Fixed | string;
  selectable?: (row: T, index: number) => boolean;
  reserveSelection?: boolean;
  filterMethod?: FilterMethods<T>;
  filteredValue?: string[];
  filterPlacement?: string;
  filterMultiple?: boolean;
  filterClassName?: string;
  index?: number | ((index: number) => number);
  sortOrders?: ("ascending" | "descending" | null)[];
  renderCell?: (data: any) => void;
  colSpan?: number;
  rowSpan?: number;
  level?: number;
  order?: string;
  isColumnGroup?: boolean;
  isSubColumn?: boolean;
  columns?: TableColumnCtx<T>[];
  getColumnIndex?: () => number;
  no?: number;
  filterOpened?: boolean;

  // 更多列属性信息(依赖的ElTableColumn版本更新如果有属性不在上面列表时可以使用moreProps)
  moreProps?: TableColumnCtx<T>;
  // 如果表格全局开启了字段拖拽排序，可以设置某一个列禁止拖动排序
  disableColumnSortable?: boolean;
  getOptionText?: (
    prop: string,
    row: T,
    column: ImTableColumnProps<T>
  ) => string;
  render?: (
    data: T,
    column: ImTableColumnProps<T>,
    index,
    offsetIndex
  ) => string;
  // 是否支持过滤
  filterable?: boolean;
  // 过滤图标自定义(替代插槽)
  filterIcon?: Object | Component;
  // 默认以prop作为插槽名称，如果定义了slot优先使用slot名称作为插槽名
  slot?: string;
  // 默认以${prop}-header作为列头插槽名称，如果定义了headerSlot优先使用headerSlot名称作为列头插槽名
  headerSlot?: string;
  // 列是否隐藏控制
  hidden?: boolean | "true";
  // 分组列
  children?: ImTableColumnProps<T>[];
  // 拓展信息配置
  meta?: ColumnMeta;
}

export const IndexRender = (row, column, index, offsetIndex) =>
  `${index + offsetIndex + 1}`;
