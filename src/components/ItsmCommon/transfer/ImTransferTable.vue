<template>
  <div class="im-transfer-table">
    <div class="transfer-wrap">
      <div class="transfer-left table">
        <div class="transfer-title">
          <span>{{ unselectTitle }}</span>
          <!--          <span-->
          <!--            >{{ leftCheckedRows.length }} /-->
          <!--            {{ filterUnSelectedRows.length }}</span-->
          <!--          >-->
        </div>
        <div class="transfer-query">
          <slot name="filter-left"></slot>
          <slot name="filter">
            <el-input
              v-model="leftKeyword"
              size="small"
              placeholder="请输入关键字过滤"
              clearable
            ></el-input>
          </slot>
        </div>
        <im-table
          :row-key="rowKey"
          show-checkbox
          :height="tableHeight"
          :columns="columns"
          :data="filterUnSelectedRows"
          @selection-change="
            rows => {
              leftCheckedRows = rows;
            }
          "
        >
        </im-table>
      </div>
      <div class="transfer-center">
        <span>
          <el-button
            type="primary"
            size="small"
            :disabled="leftCheckedRows.length === 0"
            icon="el-icon-arrow-right"
            title="移动至已选择"
            @click="addToSelected"
          ></el-button>
        </span>
        <span>
          <el-button
            type="primary"
            size="small"
            :disabled="rightCheckedRows.length === 0"
            icon="el-icon-arrow-left"
            title="返回至未选择"
            @click="backToUnselect"
          ></el-button>
        </span>
      </div>

      <div class="transfer-right table">
        <div class="transfer-title">
          <span>{{ selectedTitle }}</span>
          <!--          <span-->
          <!--            >{{ leftCheckedRows.length }} /-->
          <!--            {{ filterUnSelectedRows.length }}</span-->
          <!--          >-->
        </div>
        <div class="transfer-query">
          <slot name="filter-right"></slot>
          <slot name="filter">
            <el-input
              v-model="rightKeyword"
              size="small"
              placeholder="请输入关键字过滤"
              clearable
            ></el-input>
          </slot>
        </div>
        <im-table
          :row-key="rowKey"
          show-checkbox
          sortable
          :height="tableHeight"
          :columns="columns"
          :data="filterSelectedRows"
          @on-row-sort-change="handleRowSortChange"
          @selection-change="
            rows => {
              rightCheckedRows = rows;
            }
          "
        >
        </im-table>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import {
  computed,
  reactive,
  toRefs,
  PropType,
  watch,
  getCurrentInstance
} from "vue";
const { $message } = getCurrentInstance().appContext.config.globalProperties;
const emits = defineEmits(["update:modelValue", "selection-change"]);
const props = defineProps({
  // 标识
  rowKey: {
    type: String,
    default: "id"
  },
  // 当前选择keys列表
  modelValue: Array,
  allowEmpty: {
    type: Boolean,
    default: true
  },
  // 限制最小选择项
  min: {
    type: Number,
    default: 0
  },
  // 限制最大选择项
  max: {
    type: Number,
    default: 0
  },
  // 已选择区域标题
  selectedTitle: {
    type: String,
    default: "已选择列表"
  },
  // 未选择区域标题
  unselectTitle: {
    type: String,
    default: "未选择列表"
  },
  // 数据源
  data: Array,
  // 字段列表
  columns: Array,
  // 如果不设置使用selected来区分
  selectedFun: {
    type: Function as PropType<(record: any) => boolean>,
    default: (row: any): boolean => {
      return !!row.selected;
    }
  },
  filterProp: {
    type: String,
    default: "name"
  },
  // 关键字过滤
  keywordFilterFun: Function as PropType<
    (record: string, type: "selected" | "unselected") => boolean
  >,

  // 注意: 如果不设置addToSelectedFn和backToUnselectFn将使用selected属性来区分
  addToSelectedFn: {
    type: Function as PropType<(records: any) => void>,
    default: (rows: any[]) => {
      for (let row of rows) {
        row.selected = true;
      }
    }
  },
  backToUnselectFn: {
    type: Function as PropType<(records: any) => void>,
    default: (rows: any[]) => {
      for (let row of rows) {
        row.selected = false;
      }
    }
  },

  // 表格高度
  tableHeight: String
});

const {
  selectedTitle,
  unselectTitle,
  data,
  columns,
  selectedFun,
  keywordFilterFun,
  addToSelectedFn,
  backToUnselectFn
} = toRefs(props);

const state = reactive({
  leftKeyword: "",
  rightKeyword: "",
  leftCheckedRows: [],
  rightCheckedRows: [],
  selectedRows: []
});
const { leftKeyword, rightKeyword, leftCheckedRows, rightCheckedRows } =
  toRefs(state);

const filterSelectedRows = computed(() => {
  return state.selectedRows.filter((record: any) => {
    if (keywordFilterFun.value) {
      return keywordFilterFun.value(record, "selected");
    } else {
      let keywordValue = record[props.filterProp] || "";
      return keywordValue.indexOf((state.rightKeyword || "").trim()) > -1;
    }
  });
});

const filterUnSelectedRows = computed(() => {
  return (data.value || []).filter((record: any) => {
    let filterFlag = !selectedFun.value(record);
    if (!filterFlag) {
      return false;
    }
    if (keywordFilterFun.value) {
      return keywordFilterFun.value(record, "unselected");
    } else {
      let keywordValue = record[props.filterProp] || "";
      return keywordValue.indexOf((state.leftKeyword || "").trim()) > -1;
    }
  });
});

const addToSelected = () => {
  addToSelectedFn.value(leftCheckedRows.value);
  updateSelected();
};
const backToUnselect = () => {
  backToUnselectFn.value(rightCheckedRows.value);
  updateSelected();
};

const updateSelected = () => {
  // 备份上一次选择的列表
  const oldSelectedRows = [...state.selectedRows];

  state.selectedRows = data.value ? data.value.filter(selectedFun.value) : [];
  console.log("updateSelected", state.selectedRows.length);

  if (props.min > 0 && state.selectedRows.length < props.min) {
    $message.error(`已选择至少保留${props.min}条数据`);
    addToSelectedFn.value(rightCheckedRows.value);
    state.selectedRows = oldSelectedRows;
    return;
  }

  if (props.max > 0 && state.selectedRows.length > props.max) {
    $message.error(`已选择最多支持选择${props.max}条数据`);
    addToSelectedFn.value(rightCheckedRows.value);
    state.selectedRows = oldSelectedRows;
    return;
  }

  if (props.min)
    emits(
      "update:modelValue",
      state.selectedRows.map(row => row[props.rowKey || "id"])
    );
  emits("selection-change", state.selectedRows);
};

/**
 * 当表格绑定的数据为计算属性时,需要监听事件更改数据源位置;
 *
 * @param fromRow
 * @param toRow
 */
const handleRowSortChange = (fromRow: any, toRow: any) => {
  let rows = data.value;
  if (rows) {
    try {
      let originalFromIndex = rows.indexOf(fromRow);
      let originalToIndex = rows.indexOf(toRow);
      if (originalFromIndex > -1 && originalToIndex > -1) {
        const element = rows.splice(originalFromIndex, 1)[0];
        rows.splice(originalToIndex, 0, element);
        updateSelected();
      }
      // updateSelected();
    } catch (e) {}
  }
};

watch(
  () => data.value,
  val => {
    updateSelected();
  },
  {
    immediate: true
  }
);

watch(
  () => props.modelValue,
  keys => {
    if (keys) {
      state.selectedRows = data.value
        ? data.value.filter(row => {
            let id = row[props.rowKey];
            // if use keys.includes Need to ensure that the type of key is consistent
            for (let key of keys) {
              if (key == id) return true;
            }
            return false;
          })
        : [];
    }
  },
  {
    immediate: true
  }
);
</script>

<style scoped lang="scss">
.im-transfer-table {
  display: flex;
  justify-content: center;
  .transfer-wrap {
    width: 100%;
    display: flex;
    align-items: center;
    //justify-content: center;
    overflow: auto;
    .table {
      flex: 1;
      .transfer-title {
        display: flex;
        justify-content: space-between;
      }
    }
    .transfer-query {
      margin: 8px 0;
    }
    .transfer-center {
      width: 60px;
      margin: 0 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 20px;
    }
  }
}
</style>
