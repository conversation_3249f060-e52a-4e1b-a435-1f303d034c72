<template>
  <span class="text-primary font-bold">
    <template v-for="(part, index) in parts" :key="index">
      <span v-if="isKeyword(part)" class="highlighted">{{ part }}</span>
      <span v-else>{{ part }}</span>
    </template>
  </span>
</template>

<script setup lang="ts">
import {computed} from "vue";

interface Props {
  text: string;
  keyword: string;
}

const props = defineProps<Props>();

// 判断当前部分是否是关键词
const isKeyword = (part: string): boolean => {
  return part === props.keyword && props.keyword !== "";
};

// 将文本拆分为数组
const parts = computed(() => {
  if (props.keyword && props.text) {
    const regex = new RegExp(`(${props.keyword})`, "gi");
    return props.text.split(regex).filter(part => part !== "");
  }
  return [props.text];
});
</script>

<style scoped>
.highlighted {
  color: #ef0505;
  background-color: #eae685;
}
</style>
