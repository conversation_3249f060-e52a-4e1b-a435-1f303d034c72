<template>
  <div class="RealtimeDataMonitoring">
    <title-drill
      :title-height="titleHeight"
      btn-text="更多"
      @on-drill="handleDrill"
    ></title-drill>

    <div class="content" :style="style">
      <flow-table
        align="left"
        row-key="rowId"
        :columns="state.columns"
        :rows="state.rows"
        :row-height="32"
        :stripe-offset="1"
        auto-scroll
        :actual-render-rows="200"
        stripe-background="rgba(0,145,255,0.1)"
        header-background="rgba(14,47,100,0.6)"
      >
        <!--        <template #host="{ row }">-->
        <!--          <div>-->
        <!--            <span-->
        <!--              >{{ row["src_ip"] }} <span style="margin: 0 2px">→</span-->
        <!--              >{{ row["dst_ip"] }}</span-->
        <!--            >-->
        <!--          </div>-->
        <!--        </template>-->

        <template #reseverity="{ row }">
          <div
            class="reseverity-text"
            :style="{ color: getReseverityColor(row) }"
          >
            {{ getReseverityText(row) }}
          </div>
        </template>

        <template #deal_status="{ row }">
          <el-tag
            effect="dark"
            :type="row['deal_status'] == '2' ? 'success' : 'warning'"
            >{{ row["deal_status"] == "2" ? "自动封堵" : "未处置" }}</el-tag
          >
        </template>

        <template #attack_result="{ row }">
          <div>{{ getAttackResult(row["attack_result"]) }}</div>
        </template>
      </flow-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { baseComponentProps } from "@/views/modules/ddls/module";
import {
  computed,
  CSSProperties,
  getCurrentInstance,
  inject,
  reactive,
  watch
} from "vue";
const { $router } = getCurrentInstance().appContext.config.globalProperties;
import FlowTable from "@/views/modules/ddls/components/FlowTable.vue";
import { queryAlarmTimelineScreen } from "@/views/modules/situational-awareness/security-situation-nanchang/api";
import { v4 } from "uuid";
import { TitleDrill } from "@/views/modules/ddls";
import { getAttackResult } from "../../../security/event/api/SecurityEventApi";
const props = defineProps({
  ...baseComponentProps
});
const globalQuery = inject("globalQuery");
const queryParams = computed((): any => {
  return globalQuery || {};
});
const style = computed((): CSSProperties => {
  return {
    paddingTop: `${props.titleHeight}px`
  };
});

const state = reactive({
  columns: [
    {
      label: "攻击源路径",
      prop: "attachPath",
      width: "30%",
      align: "left",
      style: {
        paddingLeft: "10px"
      }
    },
    {
      label: "告警名称",
      prop: "event_name",
      width: "30%",
      align: "left"
    },
    {
      label: "风险级别",
      prop: "reseverity",
      width: "15%",
      align: "left"
    },
    {
      label: "攻击结果",
      prop: "attack_result",
      width: "15%",
      align: "left"
    },
    {
      label: "最新发现时间",
      prop: "last_time",
      width: "25%",
      align: "left"
    },
    // {
    //   label: "发现来源",
    //   prop: "event_agent",
    //   width: "25%",
    //   align: "left"
    // },
    {
      label: "处置状态",
      prop: "deal_status",
      width: "10%",
      align: "left"
    }
  ],
  rows: []
});

const setMockData = () => {
  state.rows = Array.from({ length: 100 }, () => {
    return {
      event_subtype_name: "僵尸网络",
      event_agent: "深信服态势感知平台",
      event_name: "有害程序-僵尸资产",
      last_time: "2020-11-22",
      reseverity: "5"
    };
  });
};

const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }
  state.rows = [];
  // queryAlarmTimelineScreen({
  //   conditions: [],
  //   dateRange: "1y",
  //   event_type_tag: null,
  //   orgId: "",
  //   asset_app_name: "",
  //   event_agent: "",
  //   reseverity: ["5", "4"],
  //   model: "event",
  //   pageNum: 1,
  //   pageSize: 200,
  //   ...(queryParams.value as any)
  // }).then(res => {
  //   let { totalElements, rows } = res.data || {};
  //   // shareDataContext.alarmCount = totalElements;
  //   state.rows = (rows || []).map(row => {
  //     return {
  //       rowId: v4(),
  //       // 风险主机
  //       attachPath: row.src_ip + " → " + row.dst_ip,
  //       ...row
  //     };
  //   });
  // });

  queryAlarmTimelineScreen({
    ...(queryParams.value as any)
  }).then(res => {
    let { totalElements, rows } = res.data || {};
    // shareDataContext.alarmCount = totalElements;
    // 最多返回200条数据
    state.rows = (rows || []).slice(0, 200).map(row => {
      return {
        rowId: v4(),
        // 风险主机
        attachPath: row.src_ip + " → " + row.dst_ip,
        ...row
      };
    });
  });
};

const getReseverityText = (row: any) => {
  let reseverity = row.reseverity;
  switch (reseverity) {
    case "5":
      return "严重";
    case "4":
      return "高危";
    case "3":
      return "中危";
    case "2":
      return "低危";
    case "1":
      return "信息";
    default:
      return "-";
  }
};

const getReseverityColor = (row: any) => {
  let reseverity = row.reseverity;
  switch (reseverity) {
    case "5":
      return "#A50003";
    case "4":
      return "#FF0408";
    case "3":
      return "#FF964B";
    case "2":
      return "#0091FF";
    case "1":
      return "#028015";
    default:
      return "#fff";
  }
};

const handleDrill = () => {
  $router.push({
    name: "security_deal",
    query: {
      dateRange: queryParams.value.dateRange
    }
  });
};

watch(
  () => queryParams.value,
  val => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>
<style scoped lang="scss">
.RealtimeDataMonitoring {
  position: relative;
  padding: 20px;
  width: 920px;
  height: 309px;
  .content {
    width: 100%;
    height: 280px;
  }
  :deep(.table-data-item) {
    font-size: 14px !important;
  }
}
</style>
