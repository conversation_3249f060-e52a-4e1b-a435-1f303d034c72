<script setup lang="ts">
import ChartComponent from "@/components/Echarts/ChartComponent.vue";
import {
  computed,
  getCurrentInstance,
  inject,
  onBeforeUnmount,
  onMounted,
  reactive,
  ref,
  watch
} from "vue";
const { $router } = getCurrentInstance().appContext.config.globalProperties;
import { baseComponentProps } from "@/views/modules/ddls/module";
import { queryApiEventRules } from "@/views/modules/situational-awareness/security-situation-nanchang/api";
import { TitleDrill } from "@/views/modules/ddls";
import { autoScroll } from "@/utils/scroll";
const props = defineProps({
  ...baseComponentProps
});
const legendEl = ref<HTMLElement>();
const colors = ["#FF7716", "#A866E1", "#247AF2", "#E4C513", "#66E1DF"];
const buildOption = data => {
  return <any>{
    color: colors,
    tooltip: {
      show: true,
      trigger: "item",
      confine: true
    },
    series: [
      {
        name: "告警类型分布",
        type: "pie",
        radius: [20, 60],
        roseType: "radius",
        itemStyle: {
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: false
          }
        },
        labelLine: {
          show: false
        },
        data
      }
    ]
  };
};

const state = reactive({
  items: [],
  option: buildOption([]),
  __timer: null
});

const globalQuery = inject("globalQuery");
const queryParams = computed((): any => {
  return globalQuery || {};
});

const setMockData = () => {
  state.items = [
    {
      name: "接口输出敏感数据",
      value: 401444,
      percent: 48.52
    },
    {
      name: "接口调用一小时内入参....",
      value: 164442,
      percent: 19.85
    },
    {
      name: "恶意扫描探测",
      value: 70,
      percent: 0
    },
    {
      name: "接口内容格式是否正常",
      value: 57453,
      percent: 6.94
    },
    {
      name: "其它",
      value: 50,
      percent: 0
    }
  ];
  state.option = buildOption(state.items);
};
const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }
  const maxCount = 200;
  queryApiEventRules({
    conditions: [],
    model: "",
    limit: maxCount,
    field: "ruleNames",
    headerFilter: {
      filters: []
    },
    ...queryParams.value
  }).then(res => {
    let { options } = res.data || {};
    let rows = (options || []).slice(0, maxCount);
    let items = [];
    let total = 0;
    for (let row of rows) {
      let { count = 0, label } = row;
      total += count;
      items.push({
        name: label,
        value: count
      });
    }
    for (let item of items) {
      item.percent =
        total == 0 ? 0 : parseFloat(((item.value * 100) / total).toFixed(2));
    }
    // update items
    state.items = items;
    // update option
    state.option = buildOption(items);
  });
};

const handleDrill = () => {
  $router.push({
    name: "api_risk_management",
    query: {
      dateRange: queryParams.value.dateRange
    }
  });
};

onMounted(() => {
  setTimeout(() => {
    let { timer } = autoScroll(legendEl.value);
    state.__timer = timer;
  }, 50);
});

onBeforeUnmount(() => {
  clearInterval(state.__timer);
  state.__timer = null;
});

watch(
  () => queryParams.value,
  val => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>

<template>
  <div class="TriggerEventStatistics">
    <div class="header-title">
      <!--      <img class="img-title" src="./img/TriggerEventStatistics.png" />-->
      <div class="img-title">
        <img class="title-img" src="../img/group.png" />
        <span class="title-text">API告警类型统计</span>
      </div>
      <title-drill
        :title-height="16"
        btn-text="更多"
        @on-drill="handleDrill"
        style="right: 0"
      ></title-drill>
    </div>

    <div class="pie-chart">
      <div class="pie">
        <ChartComponent
          v-if="state.items.length > 0"
          :option="state.option"
        ></ChartComponent>
        <div v-else class="empty-desc">暂无数据</div>
      </div>
      <div ref="legendEl" class="legend">
        <div
          class="legend-item"
          v-for="(item, index) in state.items"
          :key="index"
        >
          <div class="left">
            <div
              class="circle"
              :style="{
                background: colors[index % colors.length]
              }"
            ></div>
            <div class="label" :title="item.name">{{ item.name }}</div>
          </div>
          <div class="right">
            <span class="value" :title="item.value">{{ item.value }}</span>
            <el-divider direction="vertical"></el-divider>
            <span class="percent">{{ item.percent }}%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.TriggerEventStatistics {
  width: 100%;
  .header-title {
    position: relative;
    margin-bottom: 16px;
    .img-title {
      display: flex;
      align-items: center;
      gap: 6px;
      .title-text {
        font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        text-align: left;
        font-style: normal;
        text-transform: none;
        background: linear-gradient(180deg, #ffffff 0%, #0091ff 100%);
        background-clip: text;
        color: transparent;
      }
    }
  }
  .empty-desc {
    font-family: MiSans, MiSans;
    font-weight: 400;
    font-size: 14px;
    margin-bottom: 40px;
    color: #d1e4ff;
    opacity: 0.5;
  }
  .pie-chart {
    display: flex;
    align-items: center;
    justify-content: space-around;
    .pie {
      width: 136px;
      height: 136px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      .pie-chart {
        position: absolute;
        z-index: 200;
      }
    }
    .legend {
      width: 280px;
      height: 136px;
      display: flex;
      flex-direction: column;
      overflow: auto;
      &::-webkit-scrollbar {
        display: none;
      }
      &::-webkit-scrollbar-track-piece {
        display: none;
      }
      gap: 8px;
      .legend-item {
        width: 100%;
        height: 24px;
        display: flex;
        justify-content: space-between;
        gap: 12px;
        .left {
          display: flex;
          align-items: center;
          padding-left: 5px;
          gap: 10px;
          font-family: MiSans, MiSans;
          font-weight: 400;
          font-size: 14px;
          color: #d1e4ff;
          .label {
            display: block;
            max-width: 130px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .circle {
            width: 8px;
            height: 8px;
            border-radius: 4px;
          }
        }
        .right {
          //padding-right: 10px;
          display: flex;
          align-items: center;
          font-family: MiSans, MiSans;
          font-weight: 400;
          font-size: 13px;
          color: #ffffff;
          .value {
            display: block;
            max-width: 50px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .percent {
            //width: 0;
            width: 45px;
            text-align: right;
          }
        }
      }
    }
  }
}
</style>
