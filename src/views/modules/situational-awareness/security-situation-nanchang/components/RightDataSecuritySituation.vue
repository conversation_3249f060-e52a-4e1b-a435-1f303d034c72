<template>
  <div class="DataSecuritySituation">
    <!--    <title-drill-->
    <!--      :title-height="titleHeight"-->
    <!--      btn-text="更多"-->
    <!--      @on-drill="handleDrill"-->
    <!--    ></title-drill>-->

    <div class="content" :style="style">
      <api-basic-information v-bind="props"></api-basic-information>
      <!--      <interface-alarm-level-statistics-->
      <!--        v-bind="props"-->
      <!--      ></interface-alarm-level-statistics>-->
      <trigger-event-statistics v-bind="props"></trigger-event-statistics>
      <api-risk-alert-statistics v-bind="props"></api-risk-alert-statistics>
      <application-risk-alarm-statistics
        v-bind="props"
      ></application-risk-alarm-statistics>
    </div>
  </div>
</template>

<script setup lang="ts">
import { baseComponentProps } from "@/views/modules/ddls/module";
import { computed, CSSProperties, getCurrentInstance, inject } from "vue";
import ApiBasicInformation from "./rightComponents/ApiBasicInformation.vue";
import InterfaceAlarmLevelStatistics from "./rightComponents/InterfaceAlarmLevelStatistics.vue";
import TriggerEventStatistics from "./rightComponents/TriggerEventStatistics.vue";
import ApplicationRiskAlarmStatistics from "./rightComponents/ApplicationRiskAlarmStatistics.vue";
import ApiRiskAlertStatistics from "./rightComponents/ApiRiskAlertStatistics.vue";
import { TitleDrill } from "@/views/modules/ddls";
const props = defineProps({
  ...baseComponentProps
});
const style = computed((): CSSProperties => {
  return {
    paddingTop: `${props.titleHeight}px`
  };
});

const { $router } = getCurrentInstance().appContext.config.globalProperties;
const globalQuery = inject("globalQuery");
const queryParams = computed((): any => {
  return globalQuery || {};
});
const handleDrill = () => {
  $router.push({
    name: "api_risk_management",
    query: {
      dateRange: queryParams.value.dateRange
    }
  });
};
</script>
<style scoped lang="scss">
.DataSecuritySituation {
  position: relative;
  padding: 20px;
  width: 460px;
  height: 970px;
  .content {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;

    // 隐藏内部的下钻
    /*    ::v-deep(.title-drill) {
      display: none;
    }*/
  }
}
</style>
