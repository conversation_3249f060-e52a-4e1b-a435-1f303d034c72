<template>
  <div class="SecurityProtectionSituation">
    <!--    <title-drill-->
    <!--      :title-height="titleHeight"-->
    <!--      btn-text="更多"-->
    <!--      @on-drill="handleDrill"-->
    <!--    ></title-drill>-->
    <div class="content" :style="style">
      <control-asset-statistics v-bind="props"></control-asset-statistics>
      <security-incident-statistics
        v-bind="props"
      ></security-incident-statistics>
      <alarm-types-distribution v-bind="props"></alarm-types-distribution>
      <security-incidents-change-trend
        v-bind="props"
      ></security-incidents-change-trend>
      <attack-situation-top5 v-bind="props"></attack-situation-top5>
    </div>
  </div>
</template>

<script setup lang="ts">
import { baseComponentProps } from "@/views/modules/ddls/module";
import {
  computed,
  CSSProperties,
  getCurrentInstance,
  inject,
  provide,
  reactive,
  watch
} from "vue";
import ControlAssetStatistics from "./leftComponents/ControlAssetStatistics.vue";
import SecurityIncidentStatistics from "./leftComponents/SecurityIncidentStatistics.vue";
import AlarmTypesDistribution from "./leftComponents/AlarmTypesDistribution.vue";
import SecurityIncidentsChangeTrend from "./leftComponents/SecurityIncidentsChangeTrend.vue";
import AttackSituationTop5 from "./leftComponents/AttackSituationTop5.vue";
import { TitleDrill } from "@/views/modules/ddls";
const props = defineProps({
  ...baseComponentProps
});
const style = computed((): CSSProperties => {
  return {
    paddingTop: `${props.titleHeight}px`
  };
});

const { $router } = getCurrentInstance().appContext.config.globalProperties;
const globalQuery = inject("globalQuery");
const queryParams = computed((): any => {
  return globalQuery || {};
});
const handleDrill = () => {
  $router.push({
    name: "security_deal",
    query: {
      dateRange: queryParams.value.dateRange
    }
  });
};
</script>
<style scoped lang="scss">
.SecurityProtectionSituation {
  position: relative;
  padding: 20px;
  width: 460px;
  height: 970px;
  .content {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;

    // 隐藏内部的下钻
    //::v-deep(.title-drill) {
    //  display: none;
    //}
  }
}
</style>
