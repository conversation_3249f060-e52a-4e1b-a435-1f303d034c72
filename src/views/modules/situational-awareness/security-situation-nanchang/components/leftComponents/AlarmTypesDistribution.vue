<script setup lang="ts">
import TableTop5 from "@/views/modules/situational-awareness/components/topn/TableTop5.vue";
import { computed, getCurrentInstance, inject, reactive, watch } from "vue";
const { $router } = getCurrentInstance().appContext.config.globalProperties;
import { baseComponentProps } from "@/views/modules/ddls/module";
import { eventTypeSpread } from "@/views/modules/situational-awareness/security-situation-nanchang/api";
import { TitleDrill } from "@/views/modules/ddls";

const props = defineProps({
  ...baseComponentProps
});

const computedColumns = computed(() => {
  const label = "告警类型";
  return [
    {
      label: label,
      prop: "label"
    },
    {
      label: "高危漏洞数",
      prop: "value"
    }
  ];
});

const globalQuery = inject("globalQuery");
const queryParams = computed((): any => {
  return globalQuery || {};
});
const state = reactive({
  data: []
});

const setMockData = () => {
  const labels = [
    "挖矿",
    "蠕虫",
    "普通病毒",
    "代码注入",
    "WEB框架漏洞利用攻击"
  ];
  state.data = Array.from(
    {
      length: 5
    },
    (_, index) => {
      return {
        label: labels[index % 5],
        percent: parseInt((Math.random() * 1000) % 100),
        value: parseInt((Math.random() * 1000) % 1000)
      };
    }
  );
};

const query = () => {
  if (props.selectionMode || props.designMode) {
    setMockData();
    return;
  }

  state.data = [];
  eventTypeSpread({
    ...queryParams.value
  }).then(res => {
    console.log("res", res);
    let data = (res.data?.rows || []).slice(0, 1000);
    if (Array.isArray(data)) {
      let rows = [];
      for (let item of data) {
        let { rate, eventType, count } = item;
        rows.push({
          label: eventType,
          percent: rate,
          value: count
        });
      }
      state.data = rows;
    }
  });
};

const handleDrill = () => {
  $router.push({
    name: "security_deal",
    query: {
      dateRange: queryParams.value.dateRange
    }
  });
};

watch(
  () => queryParams.value,
  val => {
    query();
  },
  {
    immediate: true,
    deep: true
  }
);
</script>

<template>
  <div class="AlarmTypesDistribution">
    <div class="header-title">
      <img class="img-title" src="./img/AlarmTypesDistribution.png" />
      <title-drill
        :title-height="16"
        btn-text="更多"
        @on-drill="handleDrill"
        style="right: 0px"
      ></title-drill>
    </div>

    <TableTop5
      class="table-top5"
      :columns="computedColumns"
      :data="state.data"
      :scroll="{
        height: 170,
        auto: true
      }"
      hidden-header
      hidden-top-col
    ></TableTop5>
  </div>
</template>

<style scoped lang="scss">
.AlarmTypesDistribution {
  width: 100%;
  .header-title {
    position: relative;
    margin-bottom: 20px;
  }
  .table-top5 {
    min-height: 165px;
    :deep(.data-row) {
      margin-bottom: 9px;
      &:last-child {
        margin-bottom: 0 !important;
      }
    }
    :deep(.label-col) {
      flex: 1;
      padding: 0 10px 0 0;
      .bar-percent {
        background: linear-gradient(
          270deg,
          #0095ff 0%,
          rgba(0, 149, 255, 0) 100%
        ) !important;
      }
    }
  }
}
</style>
