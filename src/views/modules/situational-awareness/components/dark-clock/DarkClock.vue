<script setup lang="ts">
import { onBeforeUnmount, onMounted, reactive, ref, watch } from "vue";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
dayjs.extend(utc);
dayjs.extend(timezone);
const weeks = ["日", "一", "二", "三", "四", "五", "六"];

const props = defineProps({
  nowTime: Number
});

const state = reactive({
  currentTime: 0,
  year: 2025,
  month: 1,
  day: 1,
  hour: "",
  minute: "",
  dayOfWeek: "三"
});

const clockUpdate = () => {
  let now = dayjs().tz("Asia/Shanghai");
  if (state.currentTime) {
    now = dayjs(state.currentTime).tz("Asia/Shanghai");
    // console.log("props.nowTime", now, state.currentTime);
    state.currentTime += 1000;
  }

  state.year = now.year();
  state.month = now.month() + 1;
  state.day = now.date();

  let hour = now.hour(),
    minute = now.minute();
  state.hour = hour > 9 ? hour + "" : "0" + hour;
  state.minute = minute > 9 ? minute + "" : "0" + minute;
  state.dayOfWeek = weeks[now.day()];
};

watch(
  () => props.nowTime,
  () => {
    if (props.nowTime) {
      state.currentTime = props.nowTime;
    }
  },
  { immediate: true }
);

const intervalRef = ref<number>(-1);
onMounted(() => {
  intervalRef.value = setInterval(clockUpdate, 1000) as any;
  clockUpdate();
});

onBeforeUnmount(() => {
  clearInterval(intervalRef.value);
});
</script>

<template>
  <div class="dark-clock">
    <div class="row">
      <span class="digit">{{ state.year }}</span>
      <span class="label">年</span>
      <span class="digit">{{ state.month }}</span>
      <span class="label">月</span>
      <span class="digit">{{ state.day }}</span>
      <span class="label">日</span>
    </div>
    <div class="row">
      <div>
        <span class="label">星期</span>
        <span class="digit">{{ state.dayOfWeek }}</span>
      </div>
      <div style="margin-left: 4px">
        <span class="digit">{{ state.hour }}:{{ state.minute }}</span>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.dark-clock {
  display: flex;
  flex-direction: column;
  width: 103px;
  height: 28px;
  justify-content: center;
  .row {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    line-height: 14px;
    height: 14px;
    .label {
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
    }
    .digit {
      font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
      font-weight: 400;
      font-size: 14px;
      color: #0095ff;
      line-height: 14px;
    }
  }
}
</style>
