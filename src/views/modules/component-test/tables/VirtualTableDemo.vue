<template>
  <div class="mb-20">
    <el-card>
      <template #header>虚拟滚动表格使用</template>
      <p class="mb-4">
        1. 使用virtual-scroll开启； <br />
        2.
        需要给表格指定高度（用于计算虚拟行数），可以使用像素或者比例高度或者calc动态高度
        <br />
        3. 可以使用virtual-scroll-row-height指定行高默认34 <br />
        4. 测试数据量10w <br />
      </p>
      <im-table
        show-checkbox
        :data="state.tableData"
        virtual-scroll
        :columns="baseColumns"
        height="30vh"
      >
      </im-table>
    </el-card>
  </div>
</template>
<script lang="ts" setup>
import { ImTableColumnProps } from "@/components/ItsmCommon";
import { onMounted, reactive } from "vue";

const state = reactive({
  tableData: []
});

onMounted(() => {
  let begin = Date.now();

  state.tableData = Array.from(
    {
      length: 100000
    },
    (_, index) => {
      return {
        id: index + 1,
        date: "2016-05-03",
        name: "Tom-" + index,
        address: "No. 189, Grove St, Los Angeles"
      };
    }
  );

  console.log("onMounted count ", state.tableData.length);
  console.log("use ", Date.now() - begin);
});

const baseColumns = <ImTableColumnProps<any>[]>[
  {
    label: "编号",
    align: "left",
    prop: "id",
    width: "120px"
  },
  {
    label: "名称",
    prop: "name",
    width: "180px"
  },
  {
    label: "地址",
    prop: "address"
  },
  {
    label: "日期",
    prop: "date"
  }
];
</script>
