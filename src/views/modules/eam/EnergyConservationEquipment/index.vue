<template>
  <div class="network-device-monitor">
    <!-- 使用v-show替代动态组件，避免DOM节点引用问题 -->
    <transition mode="out-in" name="fade-transform">
      <keep-alive>
        <NetworkDeviceHomePage
          v-if="state.currentView === 'home'"
          key="home"
          :event-info="state.selectedEvent"
          @jump-to="comChange"
          @event-select="eventSelectHandler"
          @back="handleBack"
          :needsPassedData="state.needsPassedData"
        />
        <NetworkDeviceDetails
          v-else-if="state.currentView === 'detail'"
          key="detail"
          :event-info="state.selectedEvent"
          @jump-to="comChange"
          @event-select="eventSelectHandler"
          @back="handleBack"
          :needsPassedData="state.needsPassedData"
        />
      </keep-alive>
    </transition>
  </div>
</template>

<script lang="ts" setup>
import { reactive } from "vue";
import NetworkDeviceHomePage from "./components/NetworkDeviceHomePage.vue";
import NetworkDeviceDetails from "./components/NetworkDeviceDetails.vue";

// 数据对象
const state = reactive({
  // 当前显示的视图
  currentView: 'home' as 'home' | 'detail',
  // 选中的事件
  selectedEvent: null,
  // 需要传递的数据
  needsPassedData: {}
});

// 根据传入的值切换组件
const comChange = (val: string, portableData?: Object) => {
  if (val === "networkDeviceHome") {
    state.currentView = 'home';
  } else if (val === "networkDeviceDetail") {
    state.currentView = 'detail';
    state.needsPassedData = portableData || {}; // 传递需要的数据
  }
};

// 处理事件选择
const eventSelectHandler = (evt: any) => {
  state.selectedEvent = evt;
};

// 处理返回事件
const handleBack = () => {
  state.currentView = 'home';
  state.needsPassedData = {};
};
</script>

<style scoped lang="scss">
.network-device-monitor {
  width: 100%;
  height: 100%;

  // 过渡动画
  .fade-transform-enter-active,
  .fade-transform-leave-active {
    transition: all 0.3s ease;
  }

  .fade-transform-enter-from {
    opacity: 0;
    transform: translateX(30px);
  }

  .fade-transform-leave-to {
    opacity: 0;
    transform: translateX(-30px);
  }
}
</style>