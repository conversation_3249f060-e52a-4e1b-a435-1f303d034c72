<template>
  <div class="network-device-details">
    <!-- 返回按钮 -->
    <div class="detail-header">
      <el-button @click="handleBack" size="small">
        <i class="el-icon-arrow-left"></i>
        返回列表
      </el-button>
      <h2>设备详情</h2>
    </div>

    <!-- 设备基本信息 -->
    <div class="device-info-section">
      <div class="info-card">
        <h3>基本信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <label>设备ID:</label>
            <span>{{ deviceInfo.id }}</span>
          </div>
          <div class="info-item">
            <label>设备状态:</label>
            <el-tag :type="getStatusTagType(deviceInfo.status)">
              {{ getStatusText(deviceInfo.status) }}
            </el-tag>
          </div>
          <div class="info-item">
            <label>IP地址:</label>
            <span>{{ deviceInfo.ipAddress }}</span>
          </div>
          <div class="info-item">
            <label>MAC地址:</label>
            <span>{{ deviceInfo.macAddress }}</span>
          </div>
          <div class="info-item">
            <label>资产责任人:</label>
            <span>{{ deviceInfo.assetOwner }}</span>
          </div>
          <div class="info-item">
            <label>所在位置:</label>
            <span>{{ deviceInfo.location }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 运行状态监控 -->
    <div class="monitoring-section">
      <div class="monitoring-card">
        <h3>运行状态监控</h3>
        <div class="monitoring-content">
          <div class="status-chart" ref="statusChartRef"></div>
          <div class="status-metrics">
            <div class="metric-item">
              <div class="metric-value">{{ deviceMetrics.uptime }}</div>
              <div class="metric-label">运行时长</div>
            </div>
            <div class="metric-item">
              <div class="metric-value">{{ deviceMetrics.cpuUsage }}%</div>
              <div class="metric-label">CPU使用率</div>
            </div>
            <div class="metric-item">
              <div class="metric-value">{{ deviceMetrics.memoryUsage }}%</div>
              <div class="metric-label">内存使用率</div>
            </div>
            <div class="metric-item">
              <div class="metric-value">{{ deviceMetrics.networkTraffic }}</div>
              <div class="metric-label">网络流量</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 历史记录 -->
    <div class="history-section">
      <div class="history-card">
        <h3>操作历史</h3>
        <el-table :data="operationHistory" style="width: 100%">
          <el-table-column prop="timestamp" label="时间" width="180"></el-table-column>
          <el-table-column prop="operation" label="操作" width="120"></el-table-column>
          <el-table-column prop="operator" label="操作人" width="100"></el-table-column>
          <el-table-column prop="result" label="结果" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.result === '成功' ? 'success' : 'danger'">
                {{ scope.row.result }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述"></el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-section">
      <el-button type="primary" @click="handleEdit">编辑设备</el-button>
      <el-button @click="handleRefresh">刷新状态</el-button>
      <el-button type="warning" @click="handleMaintenance">维护模式</el-button>
      <el-button type="danger" @click="handleDelete">删除设备</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import * as echarts from 'echarts';
import { DeviceStatus } from '../types';

// Props
interface Props {
  eventInfo?: any;
  needsPassedData?: any;
}

const props = withDefaults(defineProps<Props>(), {
  eventInfo: null,
  needsPassedData: null
});

// Emits
const emit = defineEmits<{
  back: [];
  'jump-to': [component: string, data?: any];
}>();

// 响应式数据
const deviceInfo = reactive({
  id: 'DEV001',
  status: DeviceStatus.ONLINE,
  ipAddress: '*************',
  macAddress: '00:1B:44:11:3A:B7',
  assetOwner: '张三',
  location: '主机房A区'
});

const deviceMetrics = reactive({
  uptime: '72小时30分钟',
  cpuUsage: 45,
  memoryUsage: 68,
  networkTraffic: '1.2GB/s'
});

const operationHistory = ref([
  {
    timestamp: '2024-01-15 14:30:25',
    operation: '状态检查',
    operator: '系统',
    result: '成功',
    description: '设备状态正常'
  },
  {
    timestamp: '2024-01-15 10:15:10',
    operation: '重启设备',
    operator: '张三',
    result: '成功',
    description: '设备重启完成'
  },
  {
    timestamp: '2024-01-14 16:45:30',
    operation: '配置更新',
    operator: '李四',
    result: '成功',
    description: '更新网络配置'
  }
]);

// 图表引用
const statusChartRef = ref<HTMLElement>();
let chartInstance: echarts.ECharts | null = null;

// 方法定义
const getStatusTagType = (status: DeviceStatus) => {
  const statusMap = {
    [DeviceStatus.ONLINE]: 'success',
    [DeviceStatus.OFFLINE]: 'info',
    [DeviceStatus.FAULT]: 'danger',
    [DeviceStatus.MAINTENANCE]: 'warning'
  };
  return statusMap[status] || 'info';
};

const getStatusText = (status: DeviceStatus) => {
  const statusMap = {
    [DeviceStatus.ONLINE]: '在线',
    [DeviceStatus.OFFLINE]: '离线',
    [DeviceStatus.FAULT]: '故障',
    [DeviceStatus.MAINTENANCE]: '维护中'
  };
  return statusMap[status] || '未知';
};

const handleBack = () => {
  emit('back');
};

const handleEdit = () => {
  ElMessage.info('跳转到编辑页面');
};

const handleRefresh = () => {
  ElMessage.success('状态已刷新');
  // 这里可以重新加载设备数据
};

const handleMaintenance = () => {
  ElMessageBox.confirm('确定要将设备切换到维护模式吗？', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deviceInfo.status = DeviceStatus.MAINTENANCE;
    ElMessage.success('设备已切换到维护模式');
  }).catch(() => {
    ElMessage.info('已取消操作');
  });
};

const handleDelete = () => {
  ElMessageBox.confirm('确定要删除此设备吗？此操作不可恢复！', '确认删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success('设备已删除');
    handleBack();
  }).catch(() => {
    ElMessage.info('已取消删除');
  });
};

// 初始化状态图表
const initStatusChart = () => {
  if (!statusChartRef.value) return;

  if (chartInstance) {
    chartInstance.dispose();
  }

  chartInstance = echarts.init(statusChartRef.value);

  const option = {
    title: {
      text: '24小时状态监控',
      left: 'center',
      textStyle: {
        fontSize: 14
      }
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: Array.from({ length: 24 }, (_, i) => `${i}:00`)
    },
    yAxis: {
      type: 'value',
      max: 100
    },
    series: [
      {
        name: 'CPU使用率',
        type: 'line',
        data: Array.from({ length: 24 }, () => Math.floor(Math.random() * 80) + 10),
        itemStyle: { color: '#409eff' }
      },
      {
        name: '内存使用率',
        type: 'line',
        data: Array.from({ length: 24 }, () => Math.floor(Math.random() * 70) + 20),
        itemStyle: { color: '#67c23a' }
      }
    ]
  };

  chartInstance.setOption(option);
};

// 生命周期
onMounted(async () => {
  // 如果有传入的设备数据，使用传入的数据
  if (props.needsPassedData) {
    Object.assign(deviceInfo, props.needsPassedData);
  }

  await nextTick();
  initStatusChart();
});
</script>

<style scoped lang="scss">
.network-device-details {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;

  .detail-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 24px;

    h2 {
      margin: 0;
      color: #303133;
      font-size: 24px;
      font-weight: 600;
    }
  }

  .device-info-section,
  .monitoring-section,
  .history-section {
    margin-bottom: 24px;

    .info-card,
    .monitoring-card,
    .history-card {
      background: white;
      border-radius: 8px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      h3 {
        margin: 0 0 20px 0;
        color: #303133;
        font-size: 18px;
        font-weight: 600;
        border-bottom: 2px solid #409eff;
        padding-bottom: 8px;
      }
    }
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;

    .info-item {
      display: flex;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;

      label {
        min-width: 120px;
        color: #606266;
        font-weight: 500;
        margin-right: 12px;
      }

      span {
        color: #303133;
        flex: 1;
      }
    }
  }

  .monitoring-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
    align-items: start;

    .status-chart {
      height: 300px;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
    }

    .status-metrics {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;

      .metric-item {
        text-align: center;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;

        .metric-value {
          font-size: 24px;
          font-weight: 700;
          color: #409eff;
          margin-bottom: 8px;
        }

        .metric-label {
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }

  .action-section {
    display: flex;
    gap: 12px;
    justify-content: center;
    padding: 24px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .network-device-details {
    .monitoring-content {
      grid-template-columns: 1fr;

      .status-metrics {
        grid-template-columns: repeat(4, 1fr);
      }
    }
  }
}

@media (max-width: 768px) {
  .network-device-details {
    padding: 16px;

    .info-grid {
      grid-template-columns: 1fr;
    }

    .monitoring-content {
      .status-metrics {
        grid-template-columns: 1fr 1fr;
      }
    }

    .action-section {
      flex-direction: column;
    }
  }
}

@media (max-width: 480px) {
  .network-device-details {
    .monitoring-content {
      .status-metrics {
        grid-template-columns: 1fr;
      }
    }
  }
}
</style>
