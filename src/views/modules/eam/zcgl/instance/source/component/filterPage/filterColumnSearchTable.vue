<template>
  <div style="padding:10px;">
    <div>
      <div style="border-radius: 4px;">
        <el-row>
          <el-col :span="12" style="text-align:center">
            <el-button style="width:100%;" @click="sortData('asc')">升序</el-button>
          </el-col>
          <el-col :span="12" style="text-align:center">
            <el-button style="width:100%" @click="sortData('desc')">降序</el-button>
          </el-col>
        </el-row>
      </div>
      <el-input
        placeholder="输入关键字进行过滤"
        v-model="filterFilterText" clearable style="margin-top:5px;margin-bottom:5px;">
      </el-input>
      <div style="border-radius:4px; box-shadow:inset 0 0 1px 1px #dcdfe6;padding:2px;">
          <div style="border-bottom:1px solid #dcdfe6;padding-left:8px;padding-top:5px;padding-bottom:5px;">
            <el-checkbox :indeterminate="isIndeterminate" v-model="allChecked" @change="checkAllChange">全部（{{selectFilterCount}}）</el-checkbox>
            <el-button style="border-left:1px solid #dcdfe6;color:#666;padding-left:10px;vertical-align: 1px;" type="primary" link @click="checkNoSelect">反选</el-button>
            <el-button type="primary" link style="color:#666;margin-left:70px;vertical-align: 1px;" @click="exportColumnCount" v-loading="exportLoading">导出计数</el-button>
          </div>
        <div style="height: 208px">
          <el-auto-resizer>
            <template #default="{ height, width }">
              <el-table-v2
                :header-height="[]"
                row-key="value"
                :row-height="25"
                :columns="columns"
                :data="data"
                :width="width"
                :height="height"
                fixed
              />
            </template>
          </el-auto-resizer>
        </div>
<!--          <el-tree-->
<!--            style="height:208px;overflow:auto;"-->
<!--            filterable-->
<!--            ref="filterTreeRef"-->
<!--            class="filter-tree"-->
<!--            v-loading="filterTreeLoading"-->
<!--            :toolOptions="['expand']"-->
<!--            :default-expanded-keys="[0]"-->
<!--            :data="filterColumnData"-->
<!--            highlight-current-->
<!--            :props="treeProp"-->
<!--            node-key="value"-->
<!--            show-checkbox-->
<!--            :default-checked-keys="defaultCheckedKeys"-->
<!--            :expand-on-click-node="false"-->
<!--            @check="changeFilterCheckd"-->
<!--            >-->
<!--            <template #-="{data, node}">-->
<!--              <div v-show="data.isShow" style="width:210px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;" >-->
<!--                <span v-if="data.code.indexOf('__tree_url')>=0&&data.label.indexOf('／')>=0" :title="data.label">-->
<!--                  {{data.label.split('／')[data.label.split('／').length-1]}}-->
<!--                </span>-->
<!--                <span v-else :title="data.label">{{data.label}}</span>-->
<!--              </div>-->
<!--            </template>-->
<!--          </el-tree>-->
      </div>
      <el-row style="padding-top:15px;padding-bottom:5px;">
        <el-button type="primary" link style="color:#666;margin-left:20px;" icon="el-icon-refresh-right" @click="refreshSelect">重置</el-button>
        <el-button @click="closePopover" style="margin-left:80px;">取消</el-button>
        <el-button type="primary" @click="saveFilterQuery" style="margin-left:10px;">确定</el-button>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="tsx">
  import {queryColumnListByFilterAxios,exportExcelTempAxios} from "@/views/modules/eam/zcgl/instance/source/api/instanceModelInterface";
  import {reactive, watch, ref,unref, toRefs, nextTick, onMounted} from "vue";
  import type { FunctionalComponent } from 'vue'
  import type { ElCheckbox,CheckboxValueType, Column } from 'element-plus'
  const props = defineProps({
    categoryId:[String,Number],
    column:Object,
    colorMap:Object,
    isShow:Boolean,
    queryCondition:Object,
    propertyForm:Object,
    querySwithForm:Object,
    organizationSelectValue:String,
    queryIsShow:Boolean,
    visiable:Boolean,
    selectedCard:String
  });

  const state = reactive({
    isIndeterminate:true,
    filterTreeLoading:false,
    filterFilterText:'',
    filterColumnData:[],
    totalData:[],
    allChecked:false,
    selectFilterCount:0,
    selectProperty:'',
    allSelectKeys:[],
    defaultCheckedKeys:[],
    sort:"desc",
    exportLoading:false,
    treeProp:{'label':'label','children':'children'},
  })
  const {filterFilterText,isIndeterminate,treeProp,allChecked,selectFilterCount,exportLoading,
    filterTreeLoading,filterColumnData,defaultCheckedKeys,
  } = toRefs(state);



  type SelectionCellProps = {
    value: boolean
    intermediate?: boolean
    onChange: (value: CheckboxValueType) => void
  }

  const SelectionCell: FunctionalComponent<SelectionCellProps> = ({
                                                                    value,
                                                                    intermediate = false,
                                                                    onChange,
                                                                  }) => {
    return (
      <ElCheckbox
        onChange={onChange}
        modelValue={value}
        indeterminate={intermediate}
        />
      )
  }
  const generateColumns = (length = 1, prefix = 'column-', props?: any) =>
    Array.from({ length }).map((_, columnIndex) => ({
      key: `label`,
      dataKey: `label`,
      title: `值`,
      width: 243,
    }))



  const columns: Column<any>[] = generateColumns(1)

  columns.unshift({
    key: 'selection',
    width: 20,
    cellRenderer: ({ rowData }) => {
      const onChange = (value: CheckboxValueType) => (rowData.checked = value);
      changeFilterCheckd();
      return <SelectionCell value={rowData.checked} onChange={onChange} />
    },

    headerCellRenderer: () => {
      const _data = unref(data)
      const onChange = (value: CheckboxValueType) =>
        (data.value = _data.map((row) => {
          row.checked = value;
          return row
        }))
      const allSelected = _data.every((row) => row.checked)
      const containsChecked = _data.some((row) => row.checked)

      return (
        <SelectionCell
          value={allSelected}
          intermediate={containsChecked && !allSelected}
          onChange={onChange}
      />
    )
    },
  })
  const data = ref([])
  const filterTreeRef = ref();
  watch(filterFilterText,(val)=>{
      data.value = state.totalData.filter(item=>{
        return item.value.indexOf(val) >= 0;
      })
      let sl = data.value.filter(item=>{
        return item.checked
      })
      state.selectFilterCount = !sl?0:sl.length;
      if(data.value&&data.value.length>0&&state.selectFilterCount == data.value.length){
        state.allChecked = true;
        state.isIndeterminate = false;
      }else if(state.selectFilterCount==0){
        state.allChecked = false;
        state.isIndeterminate = false;
      }else{
        if(data.value&&data.value.length>0){
          state.allChecked = false;
          state.isIndeterminate = true;
        }else{
          state.allChecked = false;
          state.isIndeterminate = false;
        }
      }
  })
  const refreshSelect = () =>{
    if(data.value&&data.value.length>0){
      for(let i=0;i<data.value.length;i++){
        data.value[i].checked = false;
      }
    }
    state.selectFilterCount = 0;
  }
  const emit = defineEmits(["closePopover","queryInstanceTable","changeSort"])
  const closePopover = () =>{
    emit('closePopover',props.column);
  }
  const saveFilterQuery = () =>{
    let tt = [];
    if(data.value&&data.value.length>0) {
      for (let i in data.value) {
        if (data.value[i].checked) {
          tt.push(data.value[i].value);
        }
      }
    }
    if(tt&&tt.length>0){
      props.colorMap[props.column.prop] = tt;
    }else{
      props.colorMap[props.column.prop] = undefined;
    }
    closePopover();
    let mm = {};
    mm["categoryId"] = props.categoryId;
    mm["isAuth"] = true;
    emit('queryInstanceTable',mm);
  }
  const sortData = (value) =>{
    let tt = {};
    tt["column"] = props.column;
    if(tt["column"]=='category'){
      tt["column"] = 'category_id';
    }
    tt["order"] = value;
    closePopover();
    emit('changeSort',tt);
  }
  const checkAllChange = (value) =>{
    if(value){
      if(data.value&&data.value.length>0){
        for(let i in data.value){
            data.value[i].checked=true;
        }
      }
    }else{
      if(data.value&&data.value.length>0){
        for(let i in data.value){
          data.value[i].checked=false;
        }
      }
      state.selectFilterCount = 0;
    }
  }
  const checkNoSelect = () =>{
    if(data.value&&data.value.length>0){
      for(let i in data.value){
        if(data.value[i].checked){
          data.value[i].checked=false;
        }else{
          data.value[i].checked=true;
        }

      }
    }
  }
  const changeFilterCheckd = () =>{
    let select = [];
    if(data.value&&data.value.length>0){
      for(let i in data.value){
        if(data.value[i].checked){
          select.push(data.value[i]);
        }
      }
      state.selectFilterCount = select.length;
      if(data.value.length==select.length){
        state.allChecked = true;
        state.isIndeterminate = false;
      }else if(select.length==0){
        state.allChecked = false;
        state.isIndeterminate = false;
      }else{
        state.allChecked = false;
        state.isIndeterminate = true;
      }
    }
  }
  const subnet_mask_change_ip_segment = (ip_str, mask) => {
    let mark_len = 32;
    mark_len = mask;
    let nextBit = Math.min(mark_len, 8);
    let ips = ip_str.split(".");
    let maskIp = {};
    maskIp.a = ((1 << nextBit) - 1) << (8 - nextBit);
    mark_len -= 8;
    nextBit = Math.max(Math.min(mark_len, 8), 0); // 最小0位,最大8位
    maskIp.b = ((1 << nextBit) - 1) << (8 - nextBit);

    mark_len -= 8;
    nextBit = Math.max(Math.min(mark_len, 8), 0); // 最小0位,最大8位
    maskIp.c = ((1 << nextBit) - 1) << (8 - nextBit);

    mark_len -= 8;
    nextBit = Math.max(Math.min(mark_len, 8), 0); // 最小0位,最大8位
    maskIp.d = ((1 << nextBit) - 1) << (8 - nextBit);
    // 开始IP各个位置的值
    let a = ips[0] & maskIp.a;
    let b = ips[1] & maskIp.b;
    let c = ips[2] & maskIp.c;
    let d = ips[3] & maskIp.d;

    // 开始IP
    let startIp = a + "." + b + "." + c + "." + d;
    // 结束IP各个位置的值
    a = (maskIp.a ^ 255) | ips[0];
    b = (maskIp.b ^ 255) | ips[1];
    c = (maskIp.c ^ 255) | ips[2];
    d = (maskIp.d ^ 255) | ips[3];

    // 结束IP
    let endIp = a + "." + b + "." + c + "." + d;
    return iptolong(startIp) + "#" + iptolong(endIp);
  }
  const _int2iP = (num) => {
    let str;
    let tt = new Array();
    tt[0] = (num >>> 24) >>> 0;
    tt[1] = ((num << 8) >>> 24) >>> 0;
    tt[2] = (num << 16) >>> 24;
    tt[3] = (num << 24) >>> 24;
    str =
      String(tt[0]) +
      "." +
      String(tt[1]) +
      "." +
      String(tt[2]) +
      "." +
      String(tt[3]);
    return str;
  }
  const iptolong = (ip) => {
    let num = 0;
    ip = ip.split(".");
    num =
      Number(ip[0]) * 256 * 256 * 256 +
      Number(ip[1]) * 256 * 256 +
      Number(ip[2]) * 256 +
      Number(ip[3]);
    num = num >>> 0;
    return num;
  }

  /**过滤字段值*/
  const filterColumns = (column:any) =>{
    let param = {};
    state.filterTreeLoading = true;
    param["filter_column_code"] = props.column.prop;
    param["categoryId"] = props.categoryId;

    param = Object.assign({}, param, {
      dept__zone__id: props.organizationSelectValue
    })
    if (param == null || param["pageNum"] == null) {
      param["pageNum"] = 1;
      param["pageSize"] = 10;
    }
    if (props.isShow == false) {
      param["all"] = "all";
      param["allName"] = props.queryCondition.allName;
    } else {
      let propertyForm = props.propertyForm.rows;
      if (null != propertyForm) {
        //动态条件
        for (let x = 0; x < propertyForm.length; x++) {
          let colList = propertyForm[x].colList;
          for (let y = 0; y < colList.length; y++) {
            let col = colList[y];
            if (null != col.field) {
              let code = col.field.code;
              let value = col.field.value;
              let showType = col.field.showType;

              let code_id = code + "---" + showType;
              if(showType=='input'||showType=='inputSelect'||showType=='moreInput'){
                code_id = code + "---" + showType +"---" +col.field.hide;
              }
              if (
                null != value && code
              ) {
                if (col.field.value instanceof Array) {
                  if (value[0] != "" || value[1] != "") {
                    for (let i = 0; i < value.length; i++) {
                      if (!value[i]) {
                        value[i] = "null";
                      }
                    }
                    param[code_id] = value.join(",");
                  }
                } else {
                  param[code_id] = value;
                }
              }
            }
          }
        }
      } else {
        param["name"] = props.queryCondition.name;
        param["code"] = props.queryCondition.code;
        param["manager"] = props.queryCondition.manager;
        param["status"] = props.queryCondition.status;
      }
      let ipaddr = props.querySwithForm.ipaddr;
      let yanma = props.querySwithForm.yanma;
      let startIp = props.querySwithForm.startIp;
      let endIp = props.querySwithForm.endIp;
      if (ipaddr != "") {
        param["query_yanma_startIp"] = iptolong(ipaddr);
        if (yanma != "") {
          let cc = subnet_mask_change_ip_segment(ipaddr, yanma);
          param["query_yanma_startIp"] = cc.split("#")[0];
          param["query_yanma_endIp"] = cc.split("#")[1];
        }
      }
      if (startIp != "") {
        param["query_startIp"] = iptolong(startIp);
      }
      if (endIp != "") {
        param["query_endIp"] = iptolong(endIp);
      }
    }
    if(props.colorMap!=null){
      let mm = {};
      for(let key in props.colorMap){
        if(key!=props.column.prop){
          mm[key] = props.colorMap[key];
        }
      }
      param["filterColumnMap"] = JSON.stringify(mm);
    }

    if (props.selectedCard != "all") {
      param["scriptLabel"] = props.selectedCard;
    }
    state.selectProperty = column.prop;
    state.filterFilterText = '';
    state.allSelectKeys = [];
    state.defaultCheckedKeys = [];
    // filterTreeRef.value.setCheckedKeys([]);
    queryColumnListByFilterAxios(param).then(res=>{
      state.totalData  = res["data"].list;
      // if(state.totalData.length>30){
      //   for(let i = 0; i < 30; i++){
      //     state.filterColumnData.push(state.totalData[i]);
      //   }
      // }else{
        data.value = state.totalData;
      state.filterTreeLoading = false;

      nextTick(()=>{
        if(props.colorMap[column.prop]){
          let tt = props.colorMap[column.prop];
          let ss = [];
          let flag = false;
          if(data.value&&data.value.length>0){
            for(let i in data.value){
              for(let k in tt){
                if(data.value[i].value==tt[k]){
                  data.value[i]['checked'] = true;
                  flag = true;
                }
              }
            }
          }
          if(data.value.length>0&&data.value.length==tt.length){
            state.allChecked = true;
          }else{
            if(flag){
              state.isIndeterminate = true;
            }
          }
          // for(let i=0;i<tt.length;i++){
          //   if(state.allSelectKeys.indexOf(tt[i])>=0){
          //     ss.push(tt[i]);
          //   }
          // }
          // state.defaultCheckedKeys = ss;
          // // filterTreeRef.value.checkAll(false);
          // if(state.defaultCheckedKeys.length>0){
          //   filterTreeRef.value.setCheckedKeys(state.defaultCheckedKeys);
          // }else{
          //   filterTreeRef.value.setCheckedKeys([]);
          // }
          // changeFilterCheckd();
        }else{
          state.allChecked = false;
          state.isIndeterminate = false;
        }
      })

    }).catch(exp=>{
      console.log(exp);
      state.filterTreeLoading = false;
    })
  }
  const exportColumnCount = () =>{
    state.exportLoading = true;
    let params = {};
    params["fileName"] = props.column.label+"-导出计数";
    params["sheetName"] = props.column.label+"-导出计数";
    let dataList = [];
    let properties = [];
    let pro = {};
    pro["code"] = "code";
    pro["label"] = props.column.label;
    pro["dataType"] = 'String';
    properties.push(pro);
    pro = {};
    pro["code"] = "count";
    pro["label"] = "计数";
    pro["dataType"] = 'String';
    properties.push(pro);
    pro = {};
    pro["code"] = "rate";
    pro["label"] = "占比";
    pro["dataType"] = 'String';
    properties.push(pro);
    if(data.value&&data.value.length>0){
      for(let i=0;i<data.value.length;i++){
        let item = {};
        item["code"] = data.value[i].value;
        item["count"] = data.value[i].count;
        item["rate"] = data.value[i].rate+"%";
        dataList.push(item);
      }
    }
    params["properties"] = properties;
    params["dataList"] = dataList;
    exportExcelTempAxios(params).then(()=>{
      state.exportLoading = false;
    }).catch(exp=>{
      console.log(exp);
      state.exportLoading = false;
    })
  }
  onMounted(()=>{
    filterColumns(props.column)
  })
  const updateShowPo = (bool) =>{
  }
  defineExpose({
    updateShowPo
  })
</script>
