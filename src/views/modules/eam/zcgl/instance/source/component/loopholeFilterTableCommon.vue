<template>
  <div style="width: 100%">
    <avue-crud
      :data="state.userFactData"
      :option="state.columnOption"
      v-model:page="state.pagination"
      @table-loading="state.loading"
      @size-change="handlePageCurrentChange"
      @current-change="handlePageCurrentChange"
      @selection-change="selectionChange"
    >
      <template #header="{ size }">
        <el-row style="margin-bottom: 10px; margin-top: 5px">
          <el-col :span="12">
            <el-radio-group v-model="vulnerabilityListState">
              <!-- <div>{{ toolbarSlot }}</div> -->
              <el-radio-button
                v-for="(item, index) in props.tableLeftFeature"
                :label="item['name']"
                :value="item['code']"
              ></el-radio-button>
            </el-radio-group>
          </el-col>
          <el-col :span="12" style="text-align: right; padding-right: 10px">
            <template v-for="(item, index) in props.tableRightFeature">
              <el-button
                :disabled="state.checkdRow.length == 0 && item == '批量处理'"
                type="primary"
                @click="resetQuery(item as String)"
                >{{ item }}</el-button
              >
            </template>
          </el-col>
        </el-row>
      </template>
      <template #vullevel="{ row }">
        <span>
          <el-tag
            :color="getVulLevelColor(row['vullevel'] + '')"
            class="text-white border-none"
          >
            {{ getVulLevelLabel(row["vullevel"] + "") }}
          </el-tag>
        </span>
      </template>
      <template #dispatch_status="{ row }">
        <el-tag
          v-if="row.dispatch_status == '已派单'"
          class="tag"
          size="small"
          type="success"
          >已派单</el-tag
        >
        <el-tag v-else class="tag" size="small" type="danger">未派单</el-tag>
      </template>
      <template #operator="{ row, $index }">
        <!-- <div>{{ row }}</div> -->
        <el-button @click="openVulInfo(row)" type="primary" link
          >漏洞详情</el-button
        >
      </template>
    </avue-crud>

    <!-- 漏洞批处理弹窗 -->
    <el-dialog
      destroy-on-close
      v-model="dialogVisible"
      title="批量处理"
      width="500"
    >
      <el-form
        ref="ruleFormRef"
        style="max-width: 600px"
        :model="ruleForm"
        :rules="rules"
        label-width="auto"
        class="demo-ruleForm"
        :size="formSize"
        status-icon
      >
        <el-form-item label="处置动作" prop="region">
          <el-select
            clearable
            v-model="ruleForm.region"
            placeholder="请选择处置动作"
          >
            <el-option label="已修复" value="disposalof" />
            <el-option label="无需处理" value="noNeedHandle" />
            <el-option label="暂不处理" value="notHandledYet" />
          </el-select>
        </el-form-item>

        <el-form-item label="处置说明" prop="desc">
          <el-input
            placeholder="请输入处置说明"
            v-model="ruleForm.desc"
            type="textarea"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm(ruleFormRef)">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 引入漏洞详情抽屉 -->
    <el-drawer size="50%" destroy-on-close v-model="drawer" title="漏洞详情">
      <SecurityVulMonitorDetailDrawer :vulRecord="vulRecordData" />
    </el-drawer>
  </div>
</template>
<script lang="ts" setup>
// loophole
import { reactive, ref, watch, defineEmits, onMounted } from "vue";
import { ElMessage } from "element-plus";
import {
  deleteInstanceByIds,
  exportInstance
} from "@/views/modules/eam/zcgl/instance/source/api/instanceModelInterface";
import {
  vulnerabilityListMethod,
  vulnerabilityExportMethod,
  batchDisposalMethod
} from "@/views/modules/eam/zcgl/instance/source/api/vulnerabilityInformation";
import SecurityVulMonitorDetailDrawer from "@/views/modules/eam/zcgl/instance/source/component/vulnerabilityDetails/SecurityVulMonitorDetailDrawer.vue";

const props = defineProps({
  ipAddress: String,
  tableLeftFeature: Array,
  tableRightFeature: Array
});

const assetTypes = reactive([]);
const treeData = reactive([]);
const state = reactive({
  loading: false,
  // 快捷设置所有的列居中
  center: false,
  pagination: {
    total: 0,
    currentPage: 1,
    pageSize: 10,
    pageSizes: [10, 20, 40, 60]
  },
  columnOption: {
    index: true,
    indexLabel: "序号",
    selection: true,
    selectionFixed: true,
    align: "center",
    menuAlign: "center",
    maxHeight: ["330px"],
    border: true,
    stripe: true,
    addBtn: false,
    editBtn: false,
    menu: false,
    delBtn: false,
    menuWidth: 130,
    column: []
  },
  // 操作
  operator: {
    label: "操作",
    fixed: "right",
    width: "166"
  },
  checkdRow: [],
  userFactData: []
});
const vulnerabilityListState = ref("");
//监听组件属性
watch(
  props,
  (newValue: any) => {
    state.pagination.total = newValue?.userTableData?.total;
    vulnerabilityListState.value = newValue?.tableLeftFeature?.[0]?.["code"];
  },
  { deep: true, immediate: true }
);

const emit = defineEmits([
  "handlePageCurrentChangeEvent",
  "handleFilterTableDataEvent",
  "handleSelectChangeEvent"
]);
// 分页查询接口
const handlePageCurrentChange = (page: number, pageSize: number) => {
  emit(
    "handlePageCurrentChangeEvent",
    state.pagination.currentPage,
    state.pagination.pageSize
  );
  // state.pagination.pageSize = pageSize;
  // state.pagination.currentPage = page;
  vulnerabilityListAxios();
};

const dialogVisible = ref(false);
const currentSelectItems = ref([]);
const resetQuery = (name: String) => {
  if (name == "导出") {
    try {
      vulnerabilityExportMethod({
        conditions: [
          {
            field: "ip",
            value: props.ipAddress,
            fuzzyable: true,
            operator: "exact"
          }
        ],
        dateRange: "all",
        dealWith: 0,
        pageSize: state.pagination.pageSize,
        pageNum: state.pagination.currentPage
      }).then(res => {
        ElMessage.success("导出成功");
      });
    } catch (error) {}
  } else if (name == "批量处理") {
    dialogVisible.value = true;
  }

  // state.pagination.currentPage = 1;
  // handlePageCurrentChange(1, state.pagination.pageSize);
};

const vulnerabilityListAxios = () => {
  state.loading = true;
  try {
    vulnerabilityListMethod({
      conditions: [
        {
          field: "ip",
          fuzzyable: true,
          operator: "exact",
          value: props.ipAddress
        }
      ],
      dateRange: "6m",
      dealStatus: vulnerabilityListState.value,
      pageSize: state.pagination.pageSize,
      pageNum: state.pagination.currentPage
    }).then(res => {
      const newColumn = res["data"]["columns"].map(item => {
        return {
          ...item,
          hidden: true,
          label: item["name"],
          prop: item["field"],
          showOverflowTooltip: true,
          filterable: true
        };
        // if (res['data']['showList'].indexOf(item['field']) == -1) {
        //   return {
        //     ...item,
        //     "hidden": true,
        //     label: item['name'],
        //     prop: item['field'],
        //     showOverflowTooltip: true,
        //     filterable: true,
        //   }
        // } else {
        //   return {
        //     ...item,
        //     label: item['name'],
        //     prop: item['field'],
        //     "hidden": false,
        //     filterable: true,
        //     showOverflowTooltip: true,
        //     width: (item['name'] == '漏洞名称' || item['name'] == '首次发现时间' || item['name'] == '最新发现时间' || item['name'] == '工单号') ? '266px' : ""
        //   }
        // }
      });
      state.columnOption.column = [
        ...newColumn,
        {
          label: "操作",
          prop: "operator",
          width: "100px",
          fixed: "right"
        }
      ];
      state.userFactData = res["data"]["rows"];
      state.pagination.total = res["data"]["totalElements"];
      state.loading = false;
    });
  } catch (error) {
    state.loading = false;
  }
};
onMounted(() => {
  vulnerabilityListAxios();
});
watch(
  vulnerabilityListState,
  (newValue: any) => {
    vulnerabilityListAxios();
  },
  { deep: true, immediate: true }
);

// 所有页使用的表格方法分类
const homePageTableMethod = {
  Import: () => {},
  Export: (categoryId: any) => {
    // 展示导出全部
    try {
      exportInstance({ categoryId: categoryId, isAuth: "true" }).then(
        res => {}
      );
    } catch (error) {}
  },
  New: () => {},
  Deleted: (select: any, item: any) => {
    try {
      deleteInstanceByIds(select).then(res => {
        if (res?.["data"] == "success") {
          ElMessage.success("删除成功");
        } else {
          ElMessage.warning("删除失败");
        }
        emit(
          "handlePageCurrentChangeEvent",
          state.pagination.currentPage,
          state.pagination.pageSize,
          "删除"
        );
      });
    } catch (error) {
      ElMessage.warning("删除失败");
    }
  }
};
const handleLeftDeleteClick = (select: any, item: any) => {
  let tmpStr = "";
  select.map((item, index) => {
    if (index + 1 == select.length) {
      tmpStr = tmpStr + item.id;
      return;
    }
    tmpStr = tmpStr + item.id + ",";
  });
  homePageTableMethod.Deleted(tmpStr, item);
};

const selectionChange = select => {
  currentSelectItems.value = select;
  state.checkdRow = select.map(item => item.id);
};

// 表单
import type { ComponentSize, FormInstance, FormRules } from "element-plus";
import {
  getRiskLevelColor,
  getRiskLevelLabel
} from "@/views/modules/security/event/util/event_data";

interface RuleForm {
  region: string;
  desc: string;
}

const formSize = ref<ComponentSize>("default");
const ruleFormRef = ref<FormInstance>();
const ruleForm = reactive<RuleForm>({
  region: "",
  desc: ""
});
const rules = reactive<FormRules<RuleForm>>({
  region: [
    {
      required: true,
      message: "请选择处置动作",
      trigger: "change"
    }
  ],
  desc: [{ required: true, message: "请输入处置说明", trigger: "blur" }]
});

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
      const tmpList = currentSelectItems.value.map(item => {
        return {
          ips: props.ipAddress,
          vulId: item["vul_id"]
        };
      });
      try {
        batchDisposalMethod({
          dealIdea: ruleForm.desc,
          status: ruleForm.region,
          list: tmpList
        }).then(res => {
          if (!res["error"]) {
            ElMessage.success("处理成功");
          }
          dialogVisible.value = false;
          vulnerabilityListAxios();
        });
      } catch (error) {}
    } else {
    }
  });
};

// 漏洞详情
const drawer = ref(false);
const vulRecordData = ref({});
const getVulLevelColor = (id: string) => {
  switch (id) {
    case "5":
      return "#A50003";
    case "4":
      return "#FF0408";
    case "3":
      return "#FF964B";
    case "2":
      return "#0091FF";
    case "1":
      return "#028015";
    default:
      return "#000000";
  }
};
const getVulLevelLabel = (id: string) => {
  switch (id) {
    case "5":
      return "危急";
    case "4":
      return "高危";
    case "3":
      return "中危";
    case "2":
      return "低危";
    case "1":
      return "信息";
    default:
      return "风险等级";
  }
};
const openVulInfo = row => {
  drawer.value = true;
  vulRecordData.value = row;
};
</script>
<style lang="scss" scoped>
:deep(.avue-crud__header) {
  display: none;
}
</style>
