<template>
  <div>
    <div
      v-for="(groupAssetInfo, index) in groupAssetInfos"
      :key="index"
      :style="
        groupAssetInfos.length > 1 && groupAssetInfo.groupName != -1
          ? 'padding:0 10px;border-top:0px;margin-bottom: 10px;border-left: 0px;'
          : 'border: 0px;'
      "
    >
      <div v-if="index > 0" class="group-divider"></div>
      <div class="property-group-card" v-if="groupAssetInfos.length > 1">
        <el-row>
          <el-col :span="24">
            <div class="property-group">
              <GroupSvg class="group-img" style="display:inline-block;margin-right:8px;"></GroupSvg>
              <span class="group-name">{{ groupAssetInfo.groupName }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="group-items-wrap view" style="margin: 0 auto;margin-top:10px;">
        <el-form class="detail-form" label-width="120px" label-position="right">
          <el-row>
            <el-col
              v-for="(column, j) in groupAssetInfo.children"
              :span="
                column.showType == 'objectTable' || column.showType == 'table'
                  ? 24
                  : 12
              "
              :key="index + '-' + j"
            >
              <el-form-item style="position: relative">
                <template #label="{ label }">
                  <div
                    :title="
                      column.propertyUnit != null && column.propertyUnit != ''
                        ? column.name + '(' + column.propertyUnit + ')'
                        : column.name
                    "
                    style="
                      width: 100%;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                      overflow: hidden;
                      font-weight: bold;
                    "
                  >
                    {{
                      column.propertyUnit != null && column.propertyUnit != ""
                        ? column.name + "(" + column.propertyUnit + ")"
                        : column.name
                    }}：
                  </div>
                </template>
                <span
                  v-if="column.code == 'ipAddress' || column.code == 'cuid'"
                >
                  <el-tooltip :content="column.value">
                    <span
                      v-if="column.code == 'ipAddress' || column.code == 'cuid'"
                      >{{
                        !column || column.value == null || column.value == ""
                          ? ""
                          : column.value.indexOf(",") > 0
                            ? column.value.split(",")[0] + "..."
                            : column.value
                      }}</span
                    >
                    <span v-else>{{ column.value }}</span>
                  </el-tooltip>
                </span>
                <span v-else-if="column.showType == 'password'">
                  <span>●●●●●●</span>
                  <!--<el-input type="password" v-model="column.value" disabled :show-password="column.isViewPlaintext=='1'" autocomplete="new-password"></el-input>-->
                </span>
                <span v-else-if="column.showType == 'date'">
                  <span v-if="column.value">{{column.value.split(' ')[0]}}</span>
                  <span v-else>{{column.value}}</span>
                </span>
                <el-tooltip
                  :content="column.tooltipValue"
                  v-else-if="
                    column.tooltipType == '1' || column.tooltipType == '3'
                  "
                >
                  <div v-if="column.showType == 'objectTable'">
                    <el-collapse accordion v-model="ots[column.code]">
                      <el-collapse-item :name="column.code">
                        <template slot="title">
                          {{
                            column.value == undefined ||
                            column.value == null ||
                            column.value == ""
                              ? "0"
                              : column.value
                          }}
                        </template>
                        <object-table
                          :ref="column.code"
                          :categoryIdP="props.categoryId"
                          :proCode="column.code"
                          :proOcode="column.ocode"
                          type="view"
                          showOper="show"
                          :tableData.sync="column.oovalue"
                          :tableColumn="column.tableList"
                        ></object-table>
                      </el-collapse-item>
                    </el-collapse>
                  </div>
                  <span
                    v-else-if="column.showType == 'table'"
                    style="width: 100%"
                  >
                    <div>
                      <div>
                        <el-button
                          v-if="column.hide"
                          :icon="useRenderIcon('EP-Plus')"
                          @click="openUpload(column, column.code)"
                        ></el-button>
                        <el-button
                          v-else
                          :icon="useRenderIcon('EP-Minus')"
                          @click="openUpload(column, column.code)"
                        ></el-button>
                      </div>
                    </div>
                    <div
                      v-if="!column.hide"
                      style="margin-top: 10px; width: 100%"
                    >
                      <el-table :data="column.tableList" border>
                        <el-table-column
                          type="index"
                          label="序号"
                          width="80px"
                          align="center"
                        >
                        </el-table-column>
                        <el-table-column
                          label="文件大小"
                          align="center"
                          prop="fileSize"
                        >
                        </el-table-column>
                        <el-table-column
                          label="附件名称"
                          align="center"
                          prop="fileName"
                        >
                        </el-table-column>
                        <el-table-column
                          label="操作"
                          align="center"
                          prop="action"
                          width="150"
                        >
                          <template #default="scope">
                            <el-button
                              size="small"
                              @click="
                                showFileContent(
                                  scope.row.group,
                                  scope.row.path,
                                  scope.row.suffix,
                                  scope.row.fullPath,
                                  scope.row.fileName
                                )
                              "
                              >预览</el-button
                            >
                            <el-button
                              size="small"
                              @click="
                                download(scope.row.fullPath, scope.row.fileName)
                              "
                              >下载</el-button
                            >
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </span>
                  <span v-else>{{ column.value }}</span>
                </el-tooltip>
                <div v-else-if="column.tooltipType == '2'">
                  <div v-if="column.value != undefined && column.value != ''">
                    <el-popover
                      v-for="(item2, index2) in column.value.split(',')"
                      :key="item2"
                      placement="top"
                      trigger="hover"
                    >
                      <el-form label-width="160px" size="mini">
                        <el-form-item
                          v-for="item1 in column.tooltipValue.columns || []"
                          :label="item1.title + '：'"
                          :title="item1.title + '：'"
                          style="border-bottom: 2px solid #eee"
                          :key="item1.key"
                          :prop="item1.key"
                        >
                          <div style="text-align: center; font-size: 12px">
                            {{ column.tooltipValue.rows[index2][item1.key] }}
                          </div>
                        </el-form-item>
                      </el-form>
                      <span
                        slot="reference"
                        v-if="index2 == column.value.split(',').length - 1"
                        >{{ item2 }}</span
                      >
                      <span slot="reference" v-else>{{ item2 }},</span>
                    </el-popover>
                  </div>
                  <div v-else></div>
                </div>
                <div v-else>
                  <div v-if="column.showType == 'objectTable'">
                    <el-collapse v-model="ots[column.code]" accordion>
                      <el-collapse-item :name="column.code">
                        <template slot="title">
                          {{
                            column.value == undefined ||
                            column.value == null ||
                            column.value == ""
                              ? "0"
                              : column.value
                          }}
                        </template>
                        <object-table
                          :ref="column.code"
                          :categoryIdP="props.categoryId"
                          :proCode="column.code"
                          :proOcode="column.ocode"
                          type="view"
                          showOper="show"
                          :tableData.sync="column.oovalue"
                          :tableColumn="column.tableList"
                        ></object-table>
                      </el-collapse-item>
                    </el-collapse>
                  </div>
                  <span v-else-if="column.showType == 'table'">
                    <div style="overflow: hidden">
                      <el-button
                        v-if="column.hide"
                        :icon="useRenderIcon('EP-Plus')"
                        @click="openUpload(column, column.code)"
                      ></el-button>
                      <el-button
                        v-else
                        :icon="useRenderIcon('EP-Minus')"
                        @click="openUpload(column, column.code)"
                      ></el-button>
                    </div>
                    <div
                      v-if="!column.hide"
                      style="margin-top: 10px; position: relative; width: 100%"
                    >
                      <el-table :data="column.tableList">
                        <el-table-column
                          type="index"
                          label="序号"
                          width="80px"
                          align="center"
                        >
                        </el-table-column>
                        <el-table-column
                          label="文件大小"
                          align="center"
                          prop="fileSize"
                        >
                        </el-table-column>
                        <el-table-column
                          label="附件名称"
                          align="center"
                          prop="fileName"
                        >
                        </el-table-column>
                        <el-table-column
                          label="操作"
                          align="center"
                          prop="action"
                          width="150"
                        >
                          <template #default="scope">
                            <el-button
                              size="small"
                              @click="
                                showFileContent(
                                  scope.row.group,
                                  scope.row.path,
                                  scope.row.suffix,
                                  scope.row.fullPath,
                                  scope.row.fileName
                                )
                              "
                              >预览</el-button
                            >
                            <el-button
                              size="small"
                              @click="
                                download(scope.row.fullPath, scope.row.fileName)
                              "
                              >下载</el-button
                            >
                          </template>
                        </el-table-column>
                      </el-table>
                    </div>
                  </span>
                  <span v-else>{{ column.value }}</span>
                </div>
                <span
                  v-if="
                    column.titleDesc != null &&
                    column.titleDesc != undefined &&
                    column.titleDesc != '' &&
                    column.titleDesc != 'null'
                  "
                  style="
                    position: absolute;
                    top: 0px;
                    left: -18px;
                    z-index: 1000;
                  "
                  :title="column.titleDesc"
                >
                  <i class="el-icon-question" style="opacity: 0.7" />
                </span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <el-dialog
      title="文件预览"
      class="prview-dialog"
      width="90%"
      style="height: 800px"
      top="5vh"
      append-to-body
      center
      v-model="visibleFile"
      :before-close="cancelHandler"
    >
      <div class="prview-container" v-loading="previewFileLoading">
        <iframe
          v-if="!!previewFileUrl"
          frameborder="0"
          width="100%"
          height="700px"
          style="overflow: auto"
          :src="previewFileUrl"
          ref="myIframe"
          @load="handleLoad"
        >
        </iframe>
        <div v-if="!!previewImageUrl" style="height: 700px; overflow: auto">
          <img :src="previewImageUrl" @load="imageLoad" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<style lang="scss" scoped>
:deep(.el-divider__text) {
  font-size: 16px;
  font-weight: 500;

  &::before {
    content: "";
    display: inline-block;
    width: 5px;
    height: 18px;
    background: inherit;
    background-color: #409eff;
    border: none;
    border-radius: 8px;
    vertical-align: -3px;
    margin-right: 6px;
  }
}
:deep(.el-divider--horizontal) {
  border: none;
}
:deep(.el-form-item__label) {
  text-align: right;
}
:deep(.el-form-item__content) {
  margin-left: 0 !important;
  width: 100%;

  > div {
    width: 100%;
  }
}
</style>
<script setup lang="ts">
import { onMounted, reactive, toRefs, ref } from "vue";

const sm4Key = "1B6E03BAE001B71D1AC6377806E2FF63";
import { queryInstancePropDataToolTipAxios } from "@/views/modules/eam/zcgl/instance/source/api/instanceModelInterface";
import ObjectTable from "@/views/modules/eam/zcgl/instance/source/component/objectTable.vue";
import { sm4 } from "gm-crypt";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElMessage } from "element-plus";
import GroupSvg from "../assets/group.svg";
const state = reactive({
  groupAssetInfos: [],
  ots: {}
});
const download = (fullPath, fileName) => {
  window.location.href =
    "/rest/eam-core/zcgl-common/upload/download?fullPath=" +
    fullPath +
    "&fileName=" +
    fileName;
};
const openUpload = (item, id) => {
  item.hide = !item.hide;
  // this.buttonIcon=item.field.hide?"el-icon-remove-outline": "el-icon-circle-plus-outline";
  // this.$set(item, "hide", item.hide);
};
const queryInstancePropDataToolTip = () => {
  var params = {};
  params.instanceId = props.instanceId;
  params.categoryId = props.categoryId;
  params.isAuth = true;
  params.showType = props.showType;
  queryInstancePropDataToolTipAxios(params).then(res => {
    state.groupAssetInfos = res.data.list;
    for (var i = 0; i < state.groupAssetInfos.length; i++) {
      if (!state.groupAssetInfos[i].children) {
        continue;
      } else {
        for (var ii = 0; ii < state.groupAssetInfos[i].children.length; ii++) {
          var item = state.groupAssetInfos[i].children[ii];
          if (item.showType == "objectTable") {
            state.ots[item.code] = item.code;
          }
          if (
            item.showType == "password" &&
            item.isEncryption == "1" &&
            item.value != null &&
            item.value != ""
          ) {
            item.value = sm4.decrypt(item.value, sm4Key);
          }
        }
      }
    }
  });
};
const visibleFile = ref(false);
const previewFileLoading = ref(false);
const previewImageUrl = ref(null);
const previewFileUrl = ref(null);
const myIframe = ref(null);
const showFileContent = (group, filePath, suffix, fullPath, fileName) => {
  visibleFile.value = true;
  previewFileLoading.value = true;
  // let params = {
  //   group: group,
  //   path: filePath,
  //   suffix: suffix
  // }
  if (
    suffix.endsWith("jpg") ||
    suffix.endsWith("png") ||
    suffix.endsWith("jpeg") ||
    suffix.endsWith("svg") ||
    suffix.endsWith("JPG") ||
    suffix.endsWith("PNG")
  ) {
    previewImageUrl.value =
      "/rest/eam-core/zcgl-common/upload/download?fullPath=" +
      fullPath +
      "&fileName=" +
      fileName;
  } else {
    previewFileUrl.value =
      "/rest/eam-core/zcgl-common/file/viewFiles?group=" +
      group +
      "&path=" +
      filePath +
      "&suffix=" +
      suffix;
  }
  // viewFile(params).then(res => {
  //
  //
  // }).catch(err => {
  //   console.info(err);
  // });
};
const cancelHandler = () => {
  visibleFile.value = false;
  previewImageUrl.value = null;
  previewFileUrl.value = null;
  previewFileLoading.value = false;
};
const handleLoad = () => {
  previewFileLoading.value = false;
  if (
    !myIframe.value ||
    !myIframe.value.contentWindow ||
    !myIframe.value.contentWindow.document ||
    !myIframe.value.contentWindow.document.body ||
    !myIframe.value.contentWindow.document.body.innerHTML
  ) {
    ElMessage.error("当前文件类型不支持预览");
    cancelHandler();
  } else if (
    myIframe.value.contentWindow.document.body.innerHTML.indexOf(
      "服务异常，请重试或联系开发人员"
    ) >= 0
  ) {
    ElMessage.error("文件过大，加载失败，请下载查看");
    cancelHandler();
  }
};
const imageLoad = () => {
  previewFileLoading.value = false;
};
const { groupAssetInfos, ots } = toRefs(state);
onMounted(() => {
  queryInstancePropDataToolTip();
});
const props = defineProps({
  categoryId: {
    type: [Number, String],
    default: 0
  },
  instanceId: {
    type: [Number, String],
    default: 0
  },
  routeTree: {
    type: Array
  },
  showTitle: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: "over"
  },
  cateTree: {
    type: Array
  },
  isshow: {
    type: Boolean,
    default: true
  },
  categoryName: {
    type: String,
    default: ""
  },
  showType: {
    type: String,
    default: ""
  }
});
</script>
