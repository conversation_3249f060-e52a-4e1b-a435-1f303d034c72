<template>
  <div>
    <!-- 使用splitpane组件，设置左右分割的比例 -->
    <!-- 事件表格 -->
    <div class="ml-2 mr-2 mt-2">
      <avue-crud  :data="tableData" :option="tableOption" v-model:page="tablePage" :table-loading="tableLoading"
        @refresh-change="resetTablePageAndQuery" @size-change="queryEventData" @current-change="queryEventData"
        @selection-change="selectionChangeHandler">
        <!-- 表格左侧菜单 -->
        <template #menu-left="{ size }">
          <div class="flex-sc pt-0.5">
            <!-- 分段选择器，用于选择事件处理状态 -->
            <el-segmented v-model="searchCondition.dealWith" :options="segmentData"
              @change="segmentChangeHandler"></el-segmented>
          </div>
        </template>
        <!-- 表格右侧菜单 -->
        <template #menu-right="{ size }">
          <div class="float-left flex-sc pr-3 pt-0.5 gap-3">
            <el-segmented v-model="state.dateRangeSign" :options="state.timeSegmentOptions" @change="() => {
              dateTimeRange = [];
              resetTablePageAndQuery();
            }
              ">
            </el-segmented>
            <!-- 日期选择器，用于选择事件时间范围 -->
            <el-date-picker v-model="dateTimeRange" type="daterange" range-separator="到" start-placeholder="开始日期"
              end-placeholder="结束日期" style="width: 200px" @change="() => {
                state.dateRangeSign = null;
                resetTablePageAndQuery();
              }
                " />
            <!-- 数据来源 -->
            <el-select v-model="searchCondition.event_agent" placeholder="数据来源" clearable multiple collapse-tags
              style="width: 160px" class="ml-3" @change="resetTablePageAndQuery">
              <el-option v-for="item in state.dsSelData" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <!-- 风险级别选择器，用于选择事件风险级别 -->
            <el-select v-model="searchCondition.reseverity" placeholder="风险级别" multiple collapse-tags clearable
              style="width: 150px" @change="riskLevelChangeHandler">
              <el-option v-for="item in riskLevelData" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
            <!-- 批量操作下拉菜单 -->
            <el-dropdown>
              <span class="flex-sc text-primary font-bold">
                批量操作
                <IconifyIconOffline icon="EP-ArrowDown" />
              </span>
              <template #dropdown>
                <div class="pt-2 pb-2">
                  <el-dropdown-menu>
                    <!-- 设置为误报 -->
                    <el-dropdown-item>
                      <el-link plain :underline="false" :size="size" type="primary"
                        :icon="useRenderIcon('RI-MailcloseFill')" :disabled="selectedEvents.length === 0"
                        @click="batchFalsePositivesHandler">
                        设置为误报
                      </el-link>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </div>
              </template>
            </el-dropdown>
            <!-- 导出事件数据 -->
            <el-tooltip content="导出事件数据" placement="top" :open-delay="1000">
              <el-button :icon="useRenderIcon('EP-Download')" circle :size="size" :disabled="tableData.length == 0"
                @click="exportEventHandler" />
            </el-tooltip>
          </div>
        </template>
        <!-- 表格操作按钮 -->
        <template #menu="{ row, size, type }">
          <!-- 处置按钮 -->
          <el-button :size="size" :type="type" text :icon="useRenderIcon('EP-Checked')" @click="eventDealHandler(row)"
            v-if="row.deal_status === '1'">
            处置
          </el-button>
          <!-- 详情按钮 -->
          <el-button :size="size" :type="type" text :icon="useRenderIcon('EP-View')" @click="detailViewHandler(row)">
            详情
          </el-button>
        </template>
        <!-- 风险级别列 -->
        <template #reseverity="{ row }">
          <el-tag :color="getRiskLevelColor(row.reseverity)" class="text-white border-none">
            {{ getRiskLevelLabel(row.reseverity) }}
          </el-tag>
        </template>
        <!-- 事件处理状态列 -->
        <template #deal_status="{ row }">
          <el-tag effect="light" :type="getDealStatusType(row.deal_status)">
            {{ getSegmentLabel(row.deal_status) }}
          </el-tag>
        </template>
        <!-- 事件名称列 -->
        <template #event_name="{ row }">
          <el-text type="danger" class="font-bold">{{
            row.event_name
          }}</el-text>
        </template>
        <!-- 源IP列 -->
        <template #src_ip="{ row }">
          <!--              <el-text>{{ row.src_ip }}</el-text>-->
          <!--              <br></br>-->
          <el-popover placement="right-start" trigger="hover" width="270" @show="querySingleAsset(row.src_ip)">
            <assets-popover-content :key="getAssetInfo(row.src_ip).key" :event-info="row"
              :asset-info="getAssetInfo(row.src_ip).data" direction="src" @ip-block="ipBlockHandler" />
            <template #reference>
              <el-text class="text-primary">{{
                row.src_asset_name
              }}</el-text>
            </template>
          </el-popover>
        </template>
        <!-- 目标IP列 -->
        <template #dst_ip="{ row }">
          <!--              <el-text>{{ row.dst_ip }}</el-text>-->
          <!--              <br></br>-->
          <el-popover placement="right-start" trigger="hover" width="270">
            <assets-popover-content :key="getAssetInfo(row.dst_ip).key" :event-info="row"
              :asset-info="getAssetInfo(row.dst_ip).data" direction="dst" @ip-block="ipBlockHandler" />
            <template #reference>
              <el-text class="text-primary">{{
                row.dst_asset_name
              }}</el-text>
            </template>
          </el-popover>
        </template>

        <template #event_type_name="{ row }">
          <el-tag>{{ row.event_type_name }}</el-tag>
        </template>
      </avue-crud>
    </div>
    <event-deal-modal v-model:visible="deal.visible" :title="deal.title" :event-unit-ids="deal.unitIds"
      :default-action="deal.defaultAction" @deal-success="queryEventData" />
    <ip-block-modal v-model:visible="ipBlock.visible" title="IP封堵" :ip-address="ipBlock.ipAddress"
      :default-plug-label="ipBlock.defaultPlugLabel" />
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  getCurrentInstance,
  nextTick,
  onMounted,
  reactive,
  ref,
  toRefs,
  watch
} from "vue";
import dayjs from "dayjs";
import { Refresh } from "@element-plus/icons-vue";
import splitpane, { ContextProps } from "@/components/ReSplitPane";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElTree } from "element-plus";
import { useDeptStoreHook } from "@/store/modules/dept";
import { SimpleDeptInfo } from "@/views/system/deptmanage/api/DeptManageApi";
import { defaultPageSize, pageSizeOptions } from "@/utils/page_util";
import SearchWithColumn from "@/components/Search/SearchWithColumn.vue";
import {
  getDealStatusType,
  getRiskLevelColor,
  getRiskLevelLabel,
  getSegmentLabel,
  riskLevelData,
  segmentData
} from "@/views/modules/security/event/util/event_data";
import {
  exportEventData,
  getBusinessSystemData,
  getEventEnum,
  getEventList,
  getTagData,
  queryAssetsByIps,
  queryEventDeptTree
} from "@/views/modules/security/event/api/SecurityEventApi";
import AssetsPopoverContent from "@/views/modules/security/event/components/AssetsPopoverContent.vue";
import EventDealModal from "@/views/modules/security/event/components/EventDealModal.vue";
import IpBlockModal from "@/views/modules/security/event/components/IpBlockModal.vue";

const { $confirm, $message } =
  getCurrentInstance().appContext.config.globalProperties;

const deptTree = ref<InstanceType<typeof ElTree>>();
const appTreeRef = ref<InstanceType<typeof ElTree>>();

const settingLR: ContextProps = reactive({
  minPercent: 10,
  defaultPercent: 15,
  split: "vertical"
});

const treeProps = {
  parentId: "parentId",
  label: "deptName",
  children: "children",
  isLeaf: "leaf"
};

const props = defineProps({
  eventInfo:String
})
/**
 * 解析首层组织数据
 * @param firstLevelData 组织数据
 */
const parseFirstLevelDept = (firstLevelData: Array<SimpleDeptInfo>) => {
  if (deptTree.value) {
    nextTick(() => {
      deptTree.value!.setCurrentKey("-1");
    });
  }
  return [
    {
      deptId: "-1",
      deptName: "全部组织",
      children: firstLevelData,
      leaf: false
    }
  ];
};

const loadEventEnum = async (stateKey, field: string) => {
  const res = await getEventEnum(field);
  if (res.data && res.data.length > 0) {
    const dataArray = [];
    res.data.forEach((item: string) =>
      dataArray.push({
        label: item,
        value: item
      })
    );
    state[stateKey] = dataArray;
  }
};

// 数据来源
loadEventEnum("dsSelData", "event_agent");

const queryDeptTree = () => {
  // 清空关键字
  state.deptKeyWord = null;
  queryEventDeptTree().then(res => {
    state.deptData = Array.isArray(res.data) ? res.data : [res.data || {}];
  });
};

//首层组织数据 计算属性
const firstLevelDeptData = computed(() => {
  return parseFirstLevelDept(
    useDeptStoreHook()?.deptData.filter(
      (d: SimpleDeptInfo) => d.parentId === "-1"
    )
  );
});

//数据对象
const state = reactive({
  checkAll: true,
  activeTabName: "deptView",
  tableLoading: false,
  deptKeyWord: null,
  // deptData: firstLevelDeptData,
  deptData: [],
  appKeyWord: null,
  appData: [],
  eventTagData: [],
  columnCondition: {
    value: null,
    field: "event_name",
    fuzzy: true,
    operator: "fuzzy"
  },
  dateRangeSign: "30d",
  timeSegmentOptions: [
    {
      label: "近24小时",
      value: "1d"
    },
    {
      label: "近7天",
      value: "7d"
    },
    {
      label: "近30天",
      value: "30d"
    }
  ],
  dateTimeRange: [],
  searchCondition: {
    orgId: "",
    asset_app_name: "",
    dealWith: "1",
    event_agent: null,
    reseverity: "",
    event_type_tag: null,
    model: "event"
  },
  columnSearchOptions: [
    {
      label: "事件名称",
      value: "event_name"
    },
    {
      label: "攻击者IP",
      value: "src_ip"
    },
    {
      label: "受害者IP",
      value: "dst_ip"
    }
  ],
  tableData: [],
  // 资产信息(以ip为key)
  assetByIpMap: {},
  tablePage: {
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  deal: {
    visible: false,
    title: "",
    unitIds: [],
    defaultAction: null
  },
  selectedEvents: [] as Array<string>,
  ipBlock: {
    visible: false,
    ipAddress: "",
    defaultPlugLabel: "安全事件"
  },
  dsSelData: []
});
const {
  activeTabName,
  tableLoading,
  deptKeyWord,
  deptData,
  appKeyWord,
  appData,
  eventTagData,
  columnCondition,
  dateTimeRange,
  searchCondition,
  columnSearchOptions,
  tableData,
  tablePage,
  deal,
  selectedEvents,
  ipBlock
} = toRefs(state);

watch(deptKeyWord, val => {
  deptTree.value!.filter(val);
});

watch(appKeyWord, val => {
  appTreeRef.value!.filter(val);
});

//定义事件
const emit = defineEmits(["jump-to", "event-select"]);

//根据页面高度设置表格高度
const tableHeight = computed(() => {
  return document.documentElement.offsetHeight - 335 - 333;
});

const tableOption = reactive({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  selection: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menuWidth: 130,
  height: tableHeight,
  rowKey: "event_uid",
  column: [
    {
      label: "最近发生时间",
      prop: "last_time",
      sortable: true,
      width: 160
    },
    {
      label: "事件名称",
      prop: "event_name",
      filters: true
    },
    {
      label: "攻击者IP",
      prop: "src_ip",
      filters: true
    },
    {
      label: "受害者IP",
      prop: "dst_ip",
      filters: true
    },
    {
      label: "风险级别",
      prop: "reseverity",
      sortable: true,
      width: 120
    },
    {
      label: "事件类型",
      prop: "event_type_name",
      sortable: true,
      width: 120
    },
    {
      label: "处置状态",
      prop: "deal_status",
      sortable: true,
      width: 120
    }
  ]
});

//加载组织树节点数据
const loadNode = (node, resolve) => {
  if (node.isLeaf) return resolve([]);
  resolve(useDeptStoreHook().getChildren(node.data.deptId));
};

//组织数据搜索
const filterDeptNode = (value: string, data: SimpleDeptInfo) => {
  if (!value) return true;
  return data.deptName.includes(value);
};

//应用数据搜索
const filterAppNode = (value: string, data: any) => {
  if (!value) return true;
  return data.assetApp.includes(value);
};

//组织树选择改变触发
const deptSelectChange = (data: any) => {
  state.searchCondition.orgId = data.deptId;
  resetTablePageAndQuery();
};

//应用选择改变
const appChangeHandler = (data: any) => {
  if (!data) {
    // appTreeRef.value.setCurrentKey(null);
  }

  state.searchCondition.asset_app_name = data?.assetApp;
  resetTablePageAndQuery();
};

//视图标签改变触发
const viewTabChangeHandler = (activeName: string) => {
  if (activeName === "deptView") {
    //处理组织视图初始化
    state.searchCondition.asset_app_name = "";
    nextTick(() => {
      if ("-1" == deptTree.value!.getCurrentKey()) {
        state.searchCondition.orgId = "-1";
        resetTablePageAndQuery();
      }
      deptTree.value!.setCurrentKey("-1");
    });
  } else if (activeName === "appView") {
    //处理应用视图初始化
    state.searchCondition.orgId = "";
    if (state.appData && state.appData.length > 0) {
      const firstData = state.appData[0];
      nextTick(() => {
        if (firstData.assetApp == appTreeRef.value!.getCurrentKey()) {
          state.searchCondition.asset_app_name = firstData.assetApp;
          resetTablePageAndQuery();
        }
        appTreeRef.value!.setCurrentKey(firstData.assetApp);
      });
    }
  }
};

//初始化默认时间范围
// const initTimeRange = () => {
//   state.dateTimeRange = [
//     dayjs().add(-30, "d").startOf("day"),
//     dayjs().endOf("day")
//   ];
// };

//加载业务系统数据
const loadAppData = async () => {
  const appRes = await getBusinessSystemData();
  state.appData = appRes.data;
  appChangeHandler(null);
};
loadAppData();

//重置分页后查询事件数据
const resetTablePageAndQuery = () => {
  state.tablePage.currentPage = 1;
  queryEventData();
};

/**
 * 日期范围(标签和列表能复用)
 */
const computedDateRange = computed(() => {
  let dateRange;
  if (state.dateRangeSign) {
    dateRange = state.dateRangeSign;
  } else {
    dateRange = [
      dayjs(state.dateTimeRange[0]).format("YYYY-MM-DD HH:mm:ss"),
      dayjs(state.dateTimeRange[1]).endOf("day").format("YYYY-MM-DD HH:mm:ss")
    ];
  }
  return dateRange;
});

//查询事件数据
const queryEventData = async () => {
  console.log("触发数据加载");
  //设置表格加载状态为true
  state.tableLoading = true;
  dealFuzzEnable();
  let params = {
    //查询条件
    conditions: state.columnCondition.value ? [state.columnCondition] : [],
    //日期范围
    dateRange: computedDateRange.value,
    //搜索条件
    ...state.searchCondition,
    //当前页码
    pageNum: state.tablePage.currentPage,
    //每页显示条数
    pageSize: state.tablePage.pageSize
  }
  if(props.eventInfo){
    params['ips'] = props.eventInfo;
  }
  //根据条件查询事件列表
  const res = await getEventList(params);
  //设置表格加载状态为false
  state.tableLoading = false;
  //设置表格数据
  state.tableData = res.data.rows;
  // 查询关联的资产
  queryAssetData(state.tableData);
  //设置表格总条数
  state.tablePage.total = res.data.totalElements;
  //加载标签数据
  await loadEventTagData();
};

const queryAssetData = rows => {
  let ipList = [];
  for (let row of rows) {
    let { src_ip, dst_ip } = row;
    if (src_ip && !ipList.includes(src_ip)) {
      ipList.push(src_ip);
      state.assetByIpMap[src_ip] = {
        key: 1
      };
    }
    if (dst_ip && !ipList.includes(dst_ip)) {
      ipList.push(dst_ip);
      state.assetByIpMap[dst_ip] = {
        key: 1
      };
    }
  }
  state.assetByIpMap = {};
  queryAssetsByIps(ipList.join(","))
    .then(res => {
      let assets = res.data || [];
      for (let asset of assets) {
        // state.assetByIpMap
        let { ipAddress } = asset;
        state.assetByIpMap[ipAddress] = {
          key: ipAddress,
          data: asset
        };
      }
    })
    .finally(() => { });
};

const getAssetInfo = ip => {
  let assetInfo = state.assetByIpMap[ip];
  return (
    assetInfo || {
      key: 1
    }
  );
};

// 查询当个ip绑定的资产（防止重复查询）
const querySingleAsset = ipAddress => {
  let assetInfo = state.assetByIpMap[ipAddress];
  if (assetInfo && assetInfo.data) {
    return;
  }
  // 防止重复查询
  state.assetByIpMap[ipAddress] = {
    key: 1,
    data: {}
  };
  queryAssetsByIps(ipAddress)
    .then(res => {
      let assets = res.data || [];
      if (assets.length > 0) {
        state.assetByIpMap[ipAddress] = {
          key: ipAddress,
          data: assets[0]
        };
      }
    })
    .finally(() => { });
};

//加载标签数据
const loadEventTagData = async () => {
  dealFuzzEnable();

  //构建查询条件
  const condition = {
    //查询条件
    conditions: state.columnCondition.value ? [state.columnCondition] : [],
    //日期范围
    dateRange: computedDateRange.value,
    //搜索条件
    ...state.searchCondition
  };
  condition.event_type_tag = "";

  //查询数据
  const tagRes = await getTagData(condition);
  const resData = tagRes.data;

  //处理标签已选中状态
  if (state.searchCondition.event_type_tag) {
    //查找已选中的标签
    const matchData = resData.find(
      (item: any) => item.tagId === state.searchCondition.event_type_tag.tagId
    );
    //设置标签已选中状态
    if (matchData) {
      matchData.checked = true;
    }
  }
  //设置标签数据
  state.eventTagData = resData;
};

//处理模糊标识
function dealFuzzEnable() {
  if (state.columnCondition.fuzzy) {
    state.columnCondition.operator = "fuzzy";
  } else {
    state.columnCondition.operator = "exact";
  }
}

//风险标签选中改变
const tagChangeHandler = (tag: any) => {
  state.searchCondition.event_type_tag = tag;
  state.checkAll = !tag;
  state.eventTagData.forEach((item: any) => {
    item.checked = item.tagId === tag?.tagId;
  });
  resetTablePageAndQuery();
};

//处置状态改变
const segmentChangeHandler = () => resetTablePageAndQuery();

//日期范围改变触发
const dateRangeChangeHandler = (range: Array<Date>) => {
  resetTablePageAndQuery();
};

//风险级别改变触发
const riskLevelChangeHandler = (level: string) => {
  resetTablePageAndQuery();
};

//重置查询条件
const resetSearchHandler = () => {
  //重置列搜索
  state.columnCondition = {
    value: null,
    field: "event_name",
    fuzzy: true,
    operator: "fuzzy"
  };
  //重置标签选择状态
  state.eventTagData.forEach((item: any) => (item.checked = false));
  state.searchCondition.event_type_tag = null;
  //重置处置状态
  state.searchCondition.dealWith = "1";
  //重置时间范围
  state.dateTimeRange = [];
  state.dateRangeSign = "30d";
  //重置风险级别
  state.searchCondition.reseverity = "";
  resetTablePageAndQuery();
};

//查看事件详情触发
const detailViewHandler = (evt: any) => {
  emit("event-select", evt);
  jumpTo("eventDetailInfo");
};

//事件处置触发-单条
const eventDealHandler = (evt: any) => {
  state.deal.title = "事件处置";
  state.deal.defaultAction = "";
  state.deal.unitIds = [evt.event_uid];
  state.deal.visible = true;
};

//表格选择改变触发
const selectionChangeHandler = (selRows: Array<any>) => {
  const selectIds = [];
  selRows.forEach(row => selectIds.push(row.event_uid));
  state.selectedEvents = selectIds;
};

//批量误报触发
const batchFalsePositivesHandler = () => {
  state.deal.title = "批量事件处置";
  state.deal.defaultAction = "人工误报";
  state.deal.unitIds = state.selectedEvents;
  state.deal.visible = true;
};

//导出事件数据触发
const exportEventHandler = () => {
  $confirm(`您确认要导出当前查询条件下的事件数据么？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  }).then(async () => {
    $message({
      message: "数据正在导出中...",
      type: "success"
    });
    await exportEventData({
      //查询条件
      conditions: state.columnCondition.value ? [state.columnCondition] : [],
      //日期范围
      dateRange: [
        dayjs(state.dateTimeRange[0]).format("YYYY-MM-DD HH:mm:ss"),
        dayjs(state.dateTimeRange[1]).endOf("day").format("YYYY-MM-DD HH:mm:ss")
      ],
      //搜索条件
      ...state.searchCondition
    });
  });
};

//IP地址封堵触发
const ipBlockHandler = (ip: string) => {
  state.ipBlock.ipAddress = ip;
  state.ipBlock.visible = true;
};

//挂载后初始化
onMounted(() => {
  // initTimeRange();
  // 查询事件组织树
  queryDeptTree();
  // queryEventData();
});

//跳转
const jumpTo = (sign: string) => {
  emit("jump-to", sign);
};
</script>
